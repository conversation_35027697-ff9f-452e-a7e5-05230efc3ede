import { AIAgent } from '../agents/aiAgent';
import { MultiStepAgent } from '../agents/multiStepAgent';
import { MemoryInMemory } from '../memory/memoryInMemory';
import { timeTool } from '../plugins/timeTool';
import { webSearchTool } from '../plugins/webSearchTool';
import { mathTool } from '../plugins/mathTool';
import { weatherTool } from '../plugins/weatherTool';
import { AgentMemory } from '../common/agent';
import { ToolPlugin } from '../common/plugin';
import * as fs from 'fs';

// 动态配置加载函数
function loadConfig() {
  try {
    return JSON.parse(fs.readFileSync('config/system.json', 'utf-8'));
  } catch (error) {
    console.warn('Failed to load config, using defaults:', error);
    return {
      model: 'custom-agent',
      plugins: ['getTime'],
      systemPrompt: '你是一个智能助手，名叫Web智能体。你可以使用各种工具来帮助用户解决问题。'
    };
  }
}

// 适配器
class MemoryAdapter implements AgentMemory {
  constructor(private store: MemoryInMemory, private agentId: string) {}
  async remember(key: string, value: any): Promise<void> {
    await this.store.save({
      id: key,
      agentId: this.agentId,
      content: JSON.stringify(value),
      createdAt: new Date(),
    });
  }
  async recall(key: string): Promise<any> {
    const rec = await this.store.get(key);
    return rec ? JSON.parse(rec.content) : null;
  }
  async search(query: string): Promise<any[]> {
    return (await this.store.search(this.agentId, query)).map(r => r.content);
  }
  async getHistory(limit = 20): Promise<any[]> {
    // 先取全部，按 createdAt 排序，取最新 limit 条
    const all = await this.store.getHistory(this.agentId, 1000);
    const sorted = all.sort((a, b) => {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    });
    return sorted.slice(-limit).map(r => r.content);
  }
}

// 所有可用的插件
const availableTools: Record<string, ToolPlugin> = {
  getTime: timeTool,
  webSearch: webSearchTool,
  math: mathTool,
  weather: weatherTool,
};

export function createAgent(agentId: string, name: string, modelOverride?: string) {
  // 动态加载最新配置
  const config = loadConfig();

  // 记忆
  const memoryStore = new MemoryInMemory();
  const memory = new MemoryAdapter(memoryStore, agentId);

  // 插件 - 根据配置动态加载
  const tools: ToolPlugin[] = [];
  const enabledPlugins = config.plugins || ['getTime'];

  for (const pluginName of enabledPlugins) {
    if (availableTools[pluginName]) {
      tools.push(availableTools[pluginName]);
    } else {
      console.warn(`Plugin ${pluginName} not found`);
    }
  }

  // 如果没有启用任何插件，至少启用时间插件
  if (tools.length === 0) {
    tools.push(timeTool);
  }

  // 模型配置
  const modelConfig = {
    model: modelOverride || config.model || 'custom-agent',
    apiKey: config.apiKeys?.openai,
    baseUrl: config.baseUrl
  };

  console.log(`Creating agent with model: ${modelConfig.model}`); // 添加日志

  // 始终使用AI智能体（支持所有模型类型，包括自定义智能体）
  return new AIAgent(agentId, name, memory, tools, modelConfig);
}

/**
 * 创建多步处理智能体
 */
export function createMultiStepAgent(agentId: string, name: string, modelOverride?: string, multiStepConfig?: any) {
  // 动态加载最新配置
  const config = loadConfig();

  // 记忆
  const memoryStore = new MemoryInMemory();
  const memory = new MemoryAdapter(memoryStore, agentId);

  // 插件 - 根据配置动态加载
  const tools: ToolPlugin[] = [];
  const enabledPlugins = config.plugins || ['getTime'];

  for (const pluginName of enabledPlugins) {
    if (availableTools[pluginName]) {
      tools.push(availableTools[pluginName]);
    } else {
      console.warn(`Plugin ${pluginName} not found`);
    }
  }

  // 如果没有启用任何插件，至少启用时间插件
  if (tools.length === 0) {
    tools.push(timeTool);
  }

  // 模型配置
  const modelConfig = {
    model: modelOverride || config.model || 'custom-agent',
    apiKey: config.apiKeys?.openai,
    baseUrl: config.baseUrl
  };

  console.log(`Creating multi-step agent with model: ${modelConfig.model}`);

  // 创建多步处理智能体
  return new MultiStepAgent(agentId, name, memory, tools, modelConfig, multiStepConfig);
}
