import { BasicAgent } from '../agents/basicAgent';
import { MemoryInMemory } from '../memory/memoryInMemory';
import { timeTool } from '../plugins/timeTool';
import { webSearchTool } from '../plugins/webSearchTool';
import { mathTool } from '../plugins/mathTool';
import { weatherTool } from '../plugins/weatherTool';
import { AgentMemory } from '../common/agent';
import { ToolPlugin } from '../common/plugin';
import * as fs from 'fs';

// 配置加载
const config = JSON.parse(fs.readFileSync('config/system.json', 'utf-8'));

// 适配器
class MemoryAdapter implements AgentMemory {
  constructor(private store: MemoryInMemory, private agentId: string) {}
  async remember(key: string, value: any): Promise<void> {
    await this.store.save({
      id: key,
      agentId: this.agentId,
      content: JSON.stringify(value),
      createdAt: new Date(),
    });
  }
  async recall(key: string): Promise<any> {
    const rec = await this.store.get(key);
    return rec ? JSON.parse(rec.content) : null;
  }
  async search(query: string): Promise<any[]> {
    return (await this.store.search(this.agentId, query)).map(r => r.content);
  }
  async getHistory(limit = 20): Promise<any[]> {
    // 先取全部，按 createdAt 排序，取最新 limit 条
    const all = await this.store.getHistory(this.agentId, 1000);
    const sorted = all.sort((a, b) => {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    });
    return sorted.slice(-limit).map(r => r.content);
  }
}

// 所有可用的插件
const availableTools: Record<string, ToolPlugin> = {
  getTime: timeTool,
  webSearch: webSearchTool,
  math: mathTool,
  weather: weatherTool,
};

export function createAgent(agentId: string, name: string) {
  // 记忆
  const memoryStore = new MemoryInMemory();
  const memory = new MemoryAdapter(memoryStore, agentId);

  // 插件 - 根据配置动态加载
  const tools: ToolPlugin[] = [];
  const enabledPlugins = config.plugins || ['getTime'];

  for (const pluginName of enabledPlugins) {
    if (availableTools[pluginName]) {
      tools.push(availableTools[pluginName]);
    } else {
      console.warn(`Plugin ${pluginName} not found`);
    }
  }

  // 如果没有启用任何插件，至少启用时间插件
  if (tools.length === 0) {
    tools.push(timeTool);
  }

  return new BasicAgent(agentId, name, memory, tools);
}
