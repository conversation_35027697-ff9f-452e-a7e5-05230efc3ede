"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeBoxValidatorCompiler = void 0;
const compiler_1 = require("@sinclair/typebox/compiler");
const value_1 = require("@sinclair/typebox/value");
__exportStar(require("@sinclair/typebox"), exports);
const TypeBoxValidatorCompiler = ({ schema, httpPart }) => {
    const typeCheck = compiler_1.TypeCompiler.Compile(schema);
    return (value) => {
        const converted = httpPart === 'body' ? value : value_1.Value.Convert(schema, value);
        if (typeCheck.Check(converted)) {
            return { value: converted };
        }
        const errors = [];
        for (const error of typeCheck.Errors(converted)) {
            errors.push({
                message: error.message,
                instancePath: error.path
            });
        }
        return {
            error: errors
        };
    };
};
exports.TypeBoxValidatorCompiler = TypeBoxValidatorCompiler;
