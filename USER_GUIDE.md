# 🚀 智能体系统使用指南

## 快速开始

### 1. 启动项目
```bash
# 同时启动前后端（推荐）
npm run dev

# 或者分别启动
npm run start:server  # 后端
cd web && npm run dev  # 前端
```

### 2. 访问应用
- 前端界面：http://localhost:5173
- 后端API：http://localhost:3001

## 🎛️ 设置配置

### 切换AI模型
1. 点击左上角的 ⚙️ **设置** 按钮
2. 在"AI模型"部分选择您想要的模型：
   - **Llama 3.2 3B**：轻量级本地模型
   - **GPT-4**：OpenAI最强模型
   - **GPT-3.5 Turbo**：OpenAI经济型模型
   - **Claude 3**：Anthropic模型
   - **自定义智能体**：本地实现
3. 点击 **保存设置**
4. 界面会立即更新显示新的模型

### 配置API密钥（可选）
1. 在设置面板中找到"API密钥"部分
2. 输入您的API密钥：
   - **OpenWeatherMap API Key**：获得更准确的天气信息
   - **Google Search API Key**：获得真实的搜索结果
3. 点击 **保存设置**

## 🔧 使用插件工具

### 方式1：快捷按钮
点击左侧插件列表中的任意插件，会自动填入命令格式。

### 方式2：手动输入
在输入框中使用以下格式：

#### 🕒 时间工具
```
#tool:getTime
```
获取当前系统时间

#### 🧮 数学计算
```
#tool:math 10 + 5 * 2
#tool:math (100 - 20) / 4
#tool:math 3.14 * 2^2
```
支持基本四则运算和括号

#### ☁️ 天气查询
```
#tool:weather 北京
#tool:weather Shanghai
#tool:weather New York
```
获取指定城市的实时天气信息

#### 🔍 网络搜索
```
#tool:webSearch 人工智能最新发展
#tool:webSearch machine learning
#tool:webSearch 今日新闻
```
搜索互联网信息

### 多工具调用
可以在一条消息中调用多个工具：
```
请帮我#tool:getTime，然后计算#tool:math 25 * 4，最后查询#tool:weather 上海
```

## 💬 聊天功能

### 发送消息
1. 在底部输入框中输入您的消息
2. 点击 📤 **发送** 按钮或按Enter键
3. 智能体会处理您的请求并回复

### 查看历史
- 左侧"记忆/历史"面板显示您的输入历史
- 主聊天区域显示完整的对话记录
- 每条消息都有时间戳

### 清空聊天
点击左侧的 🗑️ **清空聊天** 按钮可以清除所有聊天记录。

## 🎨 界面功能

### 消息格式
- **用户消息**：蓝色气泡，右对齐
- **智能体消息**：灰色气泡，左对齐，支持Markdown渲染
- **时间戳**：每条消息下方显示发送时间

### 状态指示
- **加载状态**：发送消息时显示"智能体正在思考..."
- **模型显示**：左侧显示当前使用的AI模型
- **插件状态**：插件列表显示所有可用工具

## 🔍 故障排除

### 常见问题

#### 1. 天气查询失败
- **原因**：网络连接问题或API限制
- **解决**：检查网络连接，或在设置中配置OpenWeatherMap API密钥

#### 2. 搜索结果是模拟的
- **原因**：未配置Google Search API密钥
- **解决**：在设置中配置Google Search API密钥获得真实搜索结果

#### 3. 设置无法保存
- **原因**：后端服务未启动或配置文件权限问题
- **解决**：确保后端服务正常运行，检查config目录权限

#### 4. 插件调用失败
- **原因**：命令格式错误或插件未启用
- **解决**：使用正确的格式 `#tool:插件名 参数`

### 获取帮助
- 查看控制台日志了解详细错误信息
- 检查网络连接状态
- 确认API密钥配置正确
- 重启服务解决临时问题

## 🚀 高级使用

### 自定义插件
1. 在 `src/plugins/` 目录创建新插件文件
2. 实现 `ToolPlugin` 接口
3. 在 `src/common/factory.ts` 中注册插件
4. 重启服务器

### API集成
- 查看 `API.md` 了解完整的API文档
- 使用 `curl` 或 `Postman` 测试API端点
- 集成到其他应用程序中

### 配置管理
- 编辑 `config/system.json` 修改系统配置
- 使用环境变量覆盖配置
- 支持动态配置重载

## 📞 技术支持

如果您遇到任何问题：
1. 查看项目文档（README.md、API.md、DEPLOYMENT.md）
2. 检查控制台错误日志
3. 确认所有依赖已正确安装
4. 重启服务器解决临时问题

祝您使用愉快！🎉
