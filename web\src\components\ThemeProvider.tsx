import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Theme = 'light' | 'dark' | 'auto';
type ColorScheme = 'blue' | 'purple' | 'green' | 'orange' | 'pink';

interface ThemeContextType {
  theme: Theme;
  colorScheme: ColorScheme;
  actualTheme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
  setColorScheme: (scheme: ColorScheme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>('auto');
  const [colorScheme, setColorScheme] = useState<ColorScheme>('blue');
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');

  // 检测系统主题
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // 从localStorage加载主题设置
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as Theme;
    const savedColorScheme = localStorage.getItem('colorScheme') as ColorScheme;
    
    if (savedTheme) setTheme(savedTheme);
    if (savedColorScheme) setColorScheme(savedColorScheme);
  }, []);

  // 保存主题设置到localStorage
  useEffect(() => {
    localStorage.setItem('theme', theme);
    localStorage.setItem('colorScheme', colorScheme);
  }, [theme, colorScheme]);

  // 计算实际主题
  const actualTheme = theme === 'auto' ? systemTheme : theme;

  // 主题切换
  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('auto');
    } else {
      setTheme('light');
    }
  };

  // 应用CSS变量
  useEffect(() => {
    const root = document.documentElement;
    
    // 移除所有主题类
    root.classList.remove('light', 'dark');
    root.classList.add(actualTheme);

    // 设置颜色方案CSS变量
    const colorSchemes = {
      blue: {
        primary: '59 130 246',      // blue-500
        primaryDark: '37 99 235',   // blue-600
        secondary: '147 51 234',    // purple-600
        secondaryDark: '126 34 206' // purple-700
      },
      purple: {
        primary: '147 51 234',      // purple-600
        primaryDark: '126 34 206',  // purple-700
        secondary: '236 72 153',    // pink-500
        secondaryDark: '219 39 119' // pink-600
      },
      green: {
        primary: '34 197 94',       // green-500
        primaryDark: '22 163 74',   // green-600
        secondary: '59 130 246',    // blue-500
        secondaryDark: '37 99 235'  // blue-600
      },
      orange: {
        primary: '249 115 22',      // orange-500
        primaryDark: '234 88 12',   // orange-600
        secondary: '239 68 68',     // red-500
        secondaryDark: '220 38 38'  // red-600
      },
      pink: {
        primary: '236 72 153',      // pink-500
        primaryDark: '219 39 119',  // pink-600
        secondary: '147 51 234',    // purple-600
        secondaryDark: '126 34 206' // purple-700
      }
    };

    const colors = colorSchemes[colorScheme];
    root.style.setProperty('--color-primary', colors.primary);
    root.style.setProperty('--color-primary-dark', colors.primaryDark);
    root.style.setProperty('--color-secondary', colors.secondary);
    root.style.setProperty('--color-secondary-dark', colors.secondaryDark);
  }, [actualTheme, colorScheme]);

  const value = {
    theme,
    colorScheme,
    actualTheme,
    setTheme,
    setColorScheme,
    toggleTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

// 主题选择器组件
export function ThemeSelector() {
  const { theme, colorScheme, setTheme, setColorScheme, actualTheme } = useTheme();

  const themes = [
    { value: 'light' as const, label: '浅色', icon: '☀️' },
    { value: 'dark' as const, label: '深色', icon: '🌙' },
    { value: 'auto' as const, label: '自动', icon: '🔄' }
  ];

  const colorSchemes = [
    { value: 'blue' as const, label: '蓝色', color: 'bg-blue-500' },
    { value: 'purple' as const, label: '紫色', color: 'bg-purple-600' },
    { value: 'green' as const, label: '绿色', color: 'bg-green-500' },
    { value: 'orange' as const, label: '橙色', color: 'bg-orange-500' },
    { value: 'pink' as const, label: '粉色', color: 'bg-pink-500' }
  ];

  return (
    <div className="space-y-6">
      {/* 主题模式选择 */}
      <div>
        <h4 className={`text-sm font-medium mb-3 ${
          actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
        }`}>
          主题模式
        </h4>
        <div className="grid grid-cols-3 gap-2">
          {themes.map((t) => (
            <button
              key={t.value}
              onClick={() => setTheme(t.value)}
              className={`p-3 rounded-lg border-2 transition-all ${
                theme === t.value
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : actualTheme === 'dark'
                    ? 'border-gray-600 hover:border-gray-500 bg-gray-700'
                    : 'border-gray-200 hover:border-gray-300 bg-white'
              }`}
            >
              <div className="text-lg mb-1">{t.icon}</div>
              <div className={`text-xs font-medium ${
                theme === t.value
                  ? 'text-blue-600 dark:text-blue-400'
                  : actualTheme === 'dark'
                    ? 'text-gray-300'
                    : 'text-gray-600'
              }`}>
                {t.label}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* 颜色方案选择 */}
      <div>
        <h4 className={`text-sm font-medium mb-3 ${
          actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
        }`}>
          颜色方案
        </h4>
        <div className="grid grid-cols-5 gap-2">
          {colorSchemes.map((scheme) => (
            <button
              key={scheme.value}
              onClick={() => setColorScheme(scheme.value)}
              className={`p-3 rounded-lg border-2 transition-all ${
                colorScheme === scheme.value
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : actualTheme === 'dark'
                    ? 'border-gray-600 hover:border-gray-500 bg-gray-700'
                    : 'border-gray-200 hover:border-gray-300 bg-white'
              }`}
              title={scheme.label}
            >
              <div className={`w-6 h-6 rounded-full mx-auto mb-1 ${scheme.color}`} />
              <div className={`text-xs font-medium ${
                colorScheme === scheme.value
                  ? 'text-blue-600 dark:text-blue-400'
                  : actualTheme === 'dark'
                    ? 'text-gray-300'
                    : 'text-gray-600'
              }`}>
                {scheme.label}
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
