@tailwind base;
@tailwind components;
@tailwind utilities;

/* 主题CSS变量 */
:root {
  --color-primary: 59 130 246;      /* blue-500 */
  --color-primary-dark: 37 99 235;  /* blue-600 */
  --color-secondary: 147 51 234;    /* purple-600 */
  --color-secondary-dark: 126 34 206; /* purple-700 */
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}

/* 玻璃态效果 */
.glass {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100% !important;
    margin-left: 0 !important;
  }
}

/* 深色模式下的特殊样式 */
.dark {
  color-scheme: dark;
}

/* 自定义按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-secondary)));
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, rgb(var(--color-primary-dark)), rgb(var(--color-secondary-dark)));
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(var(--color-primary), 0.3);
}

.btn-secondary {
  background: rgba(var(--color-primary), 0.1);
  color: rgb(var(--color-primary));
  border: 1px solid rgba(var(--color-primary), 0.2);
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(var(--color-primary), 0.2);
  border-color: rgba(var(--color-primary), 0.3);
}

/* 消息动画 */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-enter {
  animation: messageSlideIn 0.3s ease-out;
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.loading-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .card-hover:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 输入框焦点效果 */
.input-focus {
  transition: all 0.3s ease;
}

.input-focus:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(var(--color-primary), 0.1);
}

/* 侧边栏动画 */
.sidebar-enter {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 工具提示 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 自定义选择样式 */
::selection {
  background: rgba(var(--color-primary), 0.3);
}

.dark ::selection {
  background: rgba(var(--color-primary), 0.5);
}

/* 焦点可见性 */
.focus-visible:focus {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
}

/* 禁用状态 */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 成功状态 */
.success {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
}

/* 错误状态 */
.error {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

/* 警告状态 */
.warning {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}
