/**
 * 简单的MCP服务器实现
 */
import express from 'express';
import cors from 'cors';
import { MCPServerSimulator } from './adapter';
import { timeTool } from '../plugins/timeTool';
import { weatherTool } from '../plugins/weatherTool';
import { mathTool } from '../plugins/mathTool';
import { webSearchTool } from '../plugins/webSearchTool';

const app = express();
app.use(cors());
app.use(express.json());

// 创建MCP服务器模拟器
const mcpServer = new MCPServerSimulator([
  timeTool,
  weatherTool,
  mathTool,
  webSearchTool
]);

// MCP API端点
app.get('/tools/list', async (req, res) => {
  try {
    const result = await mcpServer.handleRequest('tools/list', {}, {
      userId: 'mcp-user',
      agentId: 'mcp-agent',
      memory: null
    });
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.post('/tools/call', async (req, res) => {
  try {
    const { name, arguments: args } = req.body;
    const result = await mcpServer.handleRequest('tools/call', {
      name,
      arguments: args || {}
    }, {
      userId: 'mcp-user',
      agentId: 'mcp-agent',
      memory: null
    });
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

app.post('/initialize', async (req, res) => {
  try {
    const result = await mcpServer.handleRequest('initialize', req.body, {
      userId: 'mcp-user',
      agentId: 'mcp-agent',
      memory: null
    });
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    server: 'mcp-server',
    version: '1.0.0'
  });
});

// 获取服务器信息
app.get('/info', (req, res) => {
  res.json({
    name: 'Web Agent MCP Server',
    version: '1.0.0',
    description: '为Web智能体提供MCP工具服务',
    protocolVersion: '2024-11-05',
    capabilities: {
      tools: {
        listChanged: false
      }
    },
    tools: mcpServer.listTools()
  });
});

const PORT = process.env.MCP_PORT || 3002;

app.listen(PORT, () => {
  console.log(`MCP Server running on http://localhost:${PORT}`);
  console.log(`Available tools: ${mcpServer.listTools().map(t => t.name).join(', ')}`);
});

export { mcpServer };
