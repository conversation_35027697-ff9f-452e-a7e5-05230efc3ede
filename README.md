# 智能体开发工作区

本项目是一个完整的智能体系统，基于TypeScript开发，包含前端界面、后端API、插件系统和记忆管理。

## 🚀 快速开始

### 安装依赖
```bash
# 安装后端依赖
npm install

# 安装前端依赖
cd web
npm install
cd ..
```

### 启动项目
```bash
# 方式1：同时启动前后端（推荐）
npm run dev

# 方式2：分别启动
# 启动后端API服务器
npm run start:server

# 启动前端开发服务器（新终端）
cd web && npm run dev
```

### 访问应用
- 前端界面：http://localhost:5173
- 后端API：http://localhost:3001

## 🎯 功能特性

### 核心功能
- ✅ 智能体对话系统
- ✅ 插件工具系统（时间、数学、天气、搜索）
- ✅ 记忆管理系统
- ✅ 前后端分离架构
- ✅ 实时聊天界面
- ✅ Markdown消息渲染
- ✅ 时间戳显示
- ✅ 聊天记录清空

### 可用插件
1. **getTime** - 获取当前时间
2. **math** - 数学计算（支持基本运算）
3. **weather** - 天气查询（支持全球城市）
4. **webSearch** - 网络搜索（模拟结果）

## 📁 项目结构

```
├── src/                    # 后端源代码
│   ├── agents/            # 智能体实现
│   ├── api/               # API服务器
│   ├── common/            # 通用接口和类型
│   ├── memory/            # 记忆系统
│   ├── plugins/           # 插件工具
│   └── index.ts           # 主入口
├── web/                   # 前端React应用
│   ├── src/
│   │   ├── App.tsx        # 主应用组件
│   │   └── ...
│   └── package.json
├── test/                  # 测试文件
├── config/                # 配置文件
└── package.json
```

## 🔧 使用方法

### 基本对话
直接在输入框中输入消息即可与智能体对话。

### 使用插件
使用 `#tool:插件名 参数` 的格式调用插件：

```
#tool:getTime
#tool:math 10 + 5 * 2
#tool:weather 上海
#tool:webSearch 人工智能
```

### 多插件调用
可以在一条消息中调用多个插件：
```
请帮我#tool:getTime，然后计算#tool:math 100 / 4
```

## 🧪 测试

```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch
```

## 🔌 插件开发

### 创建新插件
1. 在 `src/plugins/` 目录下创建新的插件文件
2. 实现 `ToolPlugin` 接口
3. 在 `src/common/factory.ts` 中注册插件
4. 更新配置文件 `config/system.json`

### 插件示例
```typescript
import { ToolPlugin, ToolContext } from '../common/plugin';

export const myTool: ToolPlugin = {
  name: 'myTool',
  description: '我的自定义工具',
  async run(input: string, context: ToolContext): Promise<string> {
    // 实现你的逻辑
    return `处理结果: ${input}`;
  },
};
```

---

## 生产级架构方案（2025-07-16）

### 1. 架构分层与技术选型

- **前端交互层**：React + Vite + TypeScript，网页为主，后续可适配桌面/移动端。
- **API 网关层**：Fastify（或 NestJS）+ TypeScript，统一入口，前后端解耦。
- **智能体编排与任务调度层**：NestJS 微服务架构，支持多智能体/多模型协作。
- **智能体/模型服务层**：Node.js/TypeScript Worker Threads，每个模型/智能体独立进程，拥有独立记忆与推理能力。
- **工具与插件层**：标准化 TypeScript 插件接口，支持第三方开发、热加载、沙箱隔离。
- **记忆与数据存储层**：PostgreSQL（结构化）、Redis（缓存）、向量数据库（如 PGVector/Milvus/Weaviate），支持大规模语义检索和历史回溯。
- **运维与监控层**：Prometheus + Grafana、Sentry。

### 2. 关键设计建议

- 每个智能体拥有独立的长期/短期记忆，支持“自我回顾”“未完成任务追踪”。
- 插件机制标准化，支持第三方 npm 包和本地脚本，沙箱执行保障安全。
- 多模型/多工具协同，通过统一协议实现解耦。
- 前端支持任务流、历史、记忆、插件市场可视化，支持语义检索。
- 记忆系统支持结构化与向量检索，便于“我做过什么”等自然语言查询。

### 3. 参考目录结构

```
/src
  /agents         # 智能体/模型服务
  /orchestrator   # 编排与调度
  /plugins        # 插件与工具
  /memory         # 记忆系统（结构化+向量）
  /api            # Fastify/NestJS API
  /frontend       # React 前端
  /common         # 类型定义、协议、工具库
```

### 4. 关键接口示例

- 智能体接口

```ts
export interface Agent {
  id: string;
  name: string;
  memory: AgentMemory;
  act(input: AgentInput): Promise<AgentOutput>;
}
```

- 插件接口

```ts
export interface ToolPlugin {
  name: string;
  description: string;
  run(input: string, context: ToolContext): Promise<string>;
}
```

- 记忆系统接口

```ts
export interface MemoryStore {
  save(record: MemoryRecord): Promise<void>;
  get(id: string): Promise<MemoryRecord | null>;
  search(agentId: string, query: string, topK?: number): Promise<MemoryRecord[]>;
  getHistory(agentId: string, limit?: number): Promise<MemoryRecord[]>;
}
```

### 5. 配置管理建议

建议在 `config/` 目录下统一管理所有配置（如 models.json, memory.json, plugins.json 等），并在主程序入口加载。
支持通过配置文件灵活切换模型、记忆存储位置、插件启用状态等。

示例：

```json
{
  "model": "llama3.2:3b",
  "memory": {
    "type": "pgvector",
    "connection": "postgres://user:pass@host:port/db"
  },
  "plugins": ["getTime", "webSearch"]
}
```

---

## 快速开始

1. 运行 `npm install` 安装依赖。
2. 使用 `npx ts-node src/index.ts` 启动你的智能体（需先创建 `src/index.ts` 文件）。

## 目录结构建议

- `src/`：源代码目录
- `test/`：测试代码目录

## 依赖

- typescript
- ts-node
- @types/node

## 其他

如需添加更多依赖或功能，请在 `package.json` 中进行配置。
