/**
 * 天气查询插件
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

// 获取API密钥
function getApiKey(): string | null {
  try {
    const configPath = path.join(process.cwd(), 'config', 'system.json');
    const configData = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configData);
    return config.apiKeys?.openweather || null;
  } catch {
    return null;
  }
}

export const weatherTool: ToolPlugin = {
  name: 'weather',
  description: '天气查询功能，可以查询指定城市的天气信息',
  async run(input: string, context: ToolContext): Promise<string> {
    try {
      // 默认城市为北京
      const city = input.trim() || '北京';
      const apiKey = getApiKey();

      // 如果有OpenWeatherMap API密钥，使用真实API
      if (apiKey) {
        return await getRealWeather(city, apiKey);
      } else {
        // 否则使用免费的wttr.in API
        return await getFreeWeather(city);
      }
    } catch (error: any) {
      console.error('Weather query error:', error);
      return `天气查询失败: ${error instanceof Error ? error.message : '未知错误'}`;
    }
  },
};

// 使用OpenWeatherMap API获取真实天气
async function getRealWeather(city: string, apiKey: string): Promise<string> {
  const response = await axios.get(`https://api.openweathermap.org/data/2.5/weather`, {
    params: {
      q: city,
      appid: apiKey,
      units: 'metric',
      lang: 'zh_cn'
    },
    timeout: 10000
  });

  const data = response.data as any;

  let result = `${city} 当前天气 (OpenWeatherMap):\n\n`;
  result += `温度: ${data.main.temp}°C (体感温度: ${data.main.feels_like}°C)\n`;
  result += `天气: ${data.weather[0].description}\n`;
  result += `湿度: ${data.main.humidity}%\n`;
  result += `气压: ${data.main.pressure} hPa\n`;
  result += `风速: ${data.wind.speed} m/s\n`;
  result += `能见度: ${data.visibility / 1000} km\n`;

  return result;
}

// 使用免费API获取天气
async function getFreeWeather(city: string): Promise<string> {
  const response = await axios.get(`https://wttr.in/${encodeURIComponent(city)}?format=j1&lang=zh`, {
    timeout: 10000,
    headers: {
      'User-Agent': 'curl/7.68.0'
    }
  });

  const data = response.data as any;

  if (!data.current_condition || !data.current_condition[0]) {
    return `无法获取 ${city} 的天气信息，请检查城市名称是否正确。`;
  }

  const current = data.current_condition[0];
  const weather = data.weather && data.weather[0];

  let result = `${city} 当前天气 (wttr.in):\n\n`;
  result += `温度: ${current.temp_C}°C (体感温度: ${current.FeelsLikeC}°C)\n`;
  result += `天气: ${current.weatherDesc && current.weatherDesc[0] ? current.weatherDesc[0].value : '未知'}\n`;
  result += `湿度: ${current.humidity}%\n`;
  result += `风速: ${current.windspeedKmph} km/h\n`;
  result += `能见度: ${current.visibility} km\n`;

  if (weather) {
    result += `\n今日天气预报:\n`;
    result += `最高温度: ${weather.maxtempC}°C\n`;
    result += `最低温度: ${weather.mintempC}°C\n`;
    result += `日出: ${weather.astronomy && weather.astronomy[0] ? weather.astronomy[0].sunrise : '未知'}\n`;
    result += `日落: ${weather.astronomy && weather.astronomy[0] ? weather.astronomy[0].sunset : '未知'}\n`;
  }

  return result;
}
