/**
 * 天气查询插件
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import axios from 'axios';

export const weatherTool: ToolPlugin = {
  name: 'weather',
  description: '天气查询功能，可以查询指定城市的天气信息',
  async run(input: string, context: ToolContext): Promise<string> {
    try {
      // 默认城市为北京
      const city = input.trim() || '北京';
      
      // 使用免费的天气API (wttr.in)
      const response = await axios.get(`https://wttr.in/${encodeURIComponent(city)}?format=j1&lang=zh`, {
        timeout: 10000,
        headers: {
          'User-Agent': 'curl/7.68.0'
        }
      });

      const data = response.data as any;

      if (!data.current_condition || !data.current_condition[0]) {
        return `无法获取 ${city} 的天气信息，请检查城市名称是否正确。`;
      }

      const current = data.current_condition[0];
      const weather = data.weather && data.weather[0];
      
      let result = `${city} 当前天气:\n\n`;
      result += `温度: ${current.temp_C}°C (体感温度: ${current.FeelsLikeC}°C)\n`;
      result += `天气: ${current.weatherDesc && current.weatherDesc[0] ? current.weatherDesc[0].value : '未知'}\n`;
      result += `湿度: ${current.humidity}%\n`;
      result += `风速: ${current.windspeedKmph} km/h\n`;
      result += `能见度: ${current.visibility} km\n`;
      
      if (weather) {
        result += `\n今日天气预报:\n`;
        result += `最高温度: ${weather.maxtempC}°C\n`;
        result += `最低温度: ${weather.mintempC}°C\n`;
        result += `日出: ${weather.astronomy && weather.astronomy[0] ? weather.astronomy[0].sunrise : '未知'}\n`;
        result += `日落: ${weather.astronomy && weather.astronomy[0] ? weather.astronomy[0].sunset : '未知'}\n`;
      }
      
      return result;
      
    } catch (error: any) {
      console.error('Weather query error:', error);
      if (error.response) {
        if (error.response.status === 404) {
          return `找不到城市 "${input}"，请检查城市名称是否正确。`;
        }
        return `天气查询失败: 网络错误 (${error.response.status || 'unknown'})`;
      }
      return `天气查询失败: ${error instanceof Error ? error.message : '未知错误'}`;
    }
  },
};
