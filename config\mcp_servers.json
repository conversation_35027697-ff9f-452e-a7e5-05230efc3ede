{"mcpServers": {"time": {"command": "uvx", "args": ["mcp-server-time"], "disabled": false, "description": "时间和时区转换工具", "tools": ["get_current_time", "convert_timezone", "format_time"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "disabled": false, "description": "网络内容获取工具，可以抓取网页内容", "tools": ["fetch_url", "fetch_html", "fetch_text"]}, "filesystem": {"command": "uvx", "args": ["mcp-server-filesystem", "./workspace"], "disabled": false, "description": "文件系统操作工具", "tools": ["read_file", "write_file", "list_directory", "create_directory"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "description": "持久化记忆系统", "tools": ["store_memory", "retrieve_memory", "search_memory"]}}, "settings": {"timeout": 30000, "maxRetries": 3, "autoRestart": true}}