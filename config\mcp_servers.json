{"mcpServers": {"time": {"command": "uvx", "args": ["mcp-server-time"], "disabled": false, "description": "时间和时区转换工具", "tools": ["get_current_time", "convert_timezone", "format_time"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "disabled": false, "description": "网络内容获取工具，可以抓取网页内容", "tools": ["fetch_url", "fetch_html", "fetch_text"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "./workspace"], "disabled": false, "description": "文件系统操作工具", "tools": ["read_file", "write_file", "list_directory", "create_directory"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "description": "持久化记忆系统", "tools": ["store_memory", "retrieve_memory", "search_memory"]}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", ".git"], "disabled": true, "description": "Git仓库操作工具", "tools": ["git_status", "git_log", "git_diff", "git_commit", "git_branch"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "disabled": false, "description": "浏览器自动化工具，可以控制浏览器进行网页操作", "tools": ["puppeteer_screenshot", "puppeteer_pdf", "puppeteer_click", "puppeteer_type"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "disabled": true, "description": "PostgreSQL数据库操作工具", "tools": ["postgres_query", "postgres_schema"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://localhost:5432/mydb"}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "disabled": true, "description": "SQLite数据库操作工具", "tools": ["sqlite_query", "sqlite_schema", "sqlite_list_tables"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequentialthinking"], "disabled": false, "description": "动态反思问题解决工具", "tools": ["create_thinking_session", "add_thought", "get_thoughts"]}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "disabled": false, "description": "测试和演示服务器，包含各种工具示例", "tools": ["echo", "add", "longRunningOperation", "getSecret<PERSON>ey"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "disabled": false, "description": "Brave搜索引擎API，提供实时网络搜索功能", "tools": ["brave_web_search"], "env": {"BRAVE_API_KEY": "your-brave-api-key-here"}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "disabled": false, "description": "GitHub API集成，支持仓库管理和代码操作", "tools": ["github_search", "github_create_repo", "github_get_file"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token-here"}}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "disabled": true, "description": "Slack集成，支持消息发送和频道管理", "tools": ["slack_send_message", "slack_list_channels"], "env": {"SLACK_BOT_TOKEN": "your-slack-bot-token-here"}}, "gdrive": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gdrive"], "disabled": true, "description": "Google Drive集成，支持文件上传下载和管理", "tools": ["gdrive_upload", "gdrive_download", "gdrive_list"], "env": {"GOOGLE_APPLICATION_CREDENTIALS": "path/to/credentials.json"}}, "youtube": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-youtube-transcript"], "disabled": false, "description": "YouTube视频转录服务，获取视频字幕和内容", "tools": ["get_youtube_transcript", "search_youtube"]}, "aws": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-aws"], "disabled": true, "description": "AWS云服务集成，支持EC2、S3等服务管理", "tools": ["aws_ec2_list", "aws_s3_upload", "aws_lambda_invoke"], "env": {"AWS_ACCESS_KEY_ID": "your-aws-access-key", "AWS_SECRET_ACCESS_KEY": "your-aws-secret-key", "AWS_REGION": "us-east-1"}}, "docker": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-docker"], "disabled": false, "description": "Docker容器管理，支持容器创建、启动、停止等操作", "tools": ["docker_list", "docker_run", "docker_stop", "docker_logs"]}}, "settings": {"timeout": 30000, "maxRetries": 3, "autoRestart": true}}