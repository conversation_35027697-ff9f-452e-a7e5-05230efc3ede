{"mcpServers": {"time": {"command": "uvx", "args": ["mcp-server-time"], "disabled": false, "description": "时间和时区转换工具", "tools": ["get_current_time", "convert_timezone", "format_time"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "disabled": false, "description": "网络内容获取工具，可以抓取网页内容", "tools": ["fetch_url", "fetch_html", "fetch_text"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "./workspace"], "disabled": false, "description": "文件系统操作工具", "tools": ["read_file", "write_file", "list_directory", "create_directory"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "description": "持久化记忆系统", "tools": ["store_memory", "retrieve_memory", "search_memory"]}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", ".git"], "disabled": true, "description": "Git仓库操作工具", "tools": ["git_status", "git_log", "git_diff", "git_commit", "git_branch"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "disabled": false, "description": "浏览器自动化工具，可以控制浏览器进行网页操作", "tools": ["puppeteer_screenshot", "puppeteer_pdf", "puppeteer_click", "puppeteer_type"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "disabled": true, "description": "PostgreSQL数据库操作工具", "tools": ["postgres_query", "postgres_schema"], "env": {"POSTGRES_CONNECTION_STRING": "postgresql://localhost:5432/mydb"}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "disabled": true, "description": "SQLite数据库操作工具", "tools": ["sqlite_query", "sqlite_schema", "sqlite_list_tables"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequentialthinking"], "disabled": false, "description": "动态反思问题解决工具", "tools": ["create_thinking_session", "add_thought", "get_thoughts"]}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "disabled": false, "description": "测试和演示服务器，包含各种工具示例", "tools": ["echo", "add", "longRunningOperation", "getSecret<PERSON>ey"]}}, "settings": {"timeout": 30000, "maxRetries": 3, "autoRestart": true}}