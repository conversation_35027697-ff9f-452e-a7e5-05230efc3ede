import React, { useState, useEffect } from 'react';

interface MCPServer {
  name: string;
  connected: boolean;
  toolCount: number;
  description: string;
  tools: string[];
}

interface ToolChain {
  id: string;
  name: string;
  description: string;
  category: string;
  triggers: string[];
  steps: Array<{
    toolName: string;
    input: string;
    dependencies?: string[];
  }>;
}

interface ToolManagerProps {
  onSendMessage: (message: string) => void;
}

// 简化的UI组件
const Card: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <div className={`bg-white rounded-lg shadow-md border ${className}`}>
    {children}
  </div>
);

const CardHeader: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="px-6 py-4 border-b border-gray-200">
    {children}
  </div>
);

const CardTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <h3 className={`text-lg font-semibold text-gray-900 ${className}`}>
    {children}
  </h3>
);

const CardContent: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <div className={`px-6 py-4 ${className}`}>
    {children}
  </div>
);

const Badge: React.FC<{ children: React.ReactNode; variant?: string; className?: string }> = ({
  children,
  variant = 'default',
  className = ''
}) => {
  const variantClasses = {
    default: 'bg-blue-100 text-blue-800',
    secondary: 'bg-gray-100 text-gray-800',
    outline: 'bg-white border border-gray-300 text-gray-700',
    destructive: 'bg-red-100 text-red-800'
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variantClasses[variant as keyof typeof variantClasses] || variantClasses.default} ${className}`}>
      {children}
    </span>
  );
};

const Button: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  size?: string;
}> = ({ children, onClick, disabled = false, className = '', size = 'default' }) => {
  const sizeClasses = {
    default: 'px-4 py-2',
    sm: 'px-3 py-1.5 text-sm'
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${sizeClasses[size as keyof typeof sizeClasses] || sizeClasses.default} ${disabled ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700'} ${className}`}
    >
      {children}
    </button>
  );
};

const ToolManager: React.FC<ToolManagerProps> = ({ onSendMessage }) => {
  const [activeTab, setActiveTab] = useState('servers');
  const [mcpServers, setMcpServers] = useState<MCPServer[]>([]);
  const [toolChains, setToolChains] = useState<ToolChain[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedTool, setSelectedTool] = useState<string>('');
  const [toolInput, setToolInput] = useState<string>('');
  const [executionHistory, setExecutionHistory] = useState<Array<{
    timestamp: string;
    tool: string;
    input: string;
    result: string;
    success: boolean;
  }>>([]);

  // 获取MCP服务器状态
  const fetchMCPStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/act', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: 'tool-manager',
          content: '#tool:mcpStatus'
        })
      });

      const data = await response.json();
      // 解析MCP状态数据
      parseMCPStatus(data.content);
    } catch (error) {
      console.error('获取MCP状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取工具链列表
  const fetchToolChains = async () => {
    try {
      const response = await fetch('/api/act', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: 'tool-manager',
          content: '#tool:toolChainManager 列表'
        })
      });
      
      const data = await response.json();
      // 解析工具链数据
      parseToolChains(data.content);
    } catch (error) {
      console.error('获取工具链失败:', error);
    }
  };

  // 解析MCP状态
  const parseMCPStatus = (content: string) => {
    // 简化的解析逻辑，实际应该更robust
    const servers: MCPServer[] = [
      { name: 'time', connected: true, toolCount: 2, description: '时间和时区转换工具', tools: ['get_current_time', 'convert_time'] },
      { name: 'fetch', connected: true, toolCount: 1, description: '网络内容获取工具', tools: ['fetch'] },
      { name: 'filesystem', connected: true, toolCount: 12, description: '文件系统操作工具', tools: ['read_file', 'write_file', 'list_directory'] },
      { name: 'memory', connected: true, toolCount: 9, description: '持久化记忆系统', tools: ['create_entities', 'search_nodes'] },
      { name: 'puppeteer', connected: true, toolCount: 7, description: '浏览器自动化工具', tools: ['puppeteer_screenshot', 'puppeteer_navigate'] },
      { name: 'everything', connected: true, toolCount: 8, description: '测试和演示工具', tools: ['echo', 'add'] }
    ];
    setMcpServers(servers);
  };

  // 解析工具链
  const parseToolChains = (content: string) => {
    // 模拟工具链数据
    const chains: ToolChain[] = [
      {
        id: 'web-analysis',
        name: '网页分析工具链',
        description: '获取网页内容并进行分析总结',
        category: 'web',
        triggers: ['分析网页', '网页分析'],
        steps: [
          { toolName: 'mcpFetch', input: '${url}' },
          { toolName: 'mcpMemory', input: '创建实体', dependencies: ['mcpFetch'] }
        ]
      },
      {
        id: 'file-processing',
        name: '文件处理工具链',
        description: '创建、写入、读取文件的完整流程',
        category: 'file',
        triggers: ['处理文件', '文件操作'],
        steps: [
          { toolName: 'mcpFileSystem', input: '创建文件' },
          { toolName: 'mcpFileSystem', input: '读取文件', dependencies: ['mcpFileSystem'] }
        ]
      }
    ];
    setToolChains(chains);
  };

  // 执行工具
  const executeTool = async () => {
    if (!selectedTool || !toolInput) return;

    const timestamp = new Date().toLocaleString();
    setLoading(true);

    try {
      const command = `#tool:${selectedTool} ${toolInput}`;
      onSendMessage(command);

      // 模拟执行结果
      const newExecution = {
        timestamp,
        tool: selectedTool,
        input: toolInput,
        result: '执行成功',
        success: true
      };

      setExecutionHistory(prev => [newExecution, ...prev.slice(0, 9)]);
      setToolInput('');
    } catch (error) {
      const newExecution = {
        timestamp,
        tool: selectedTool,
        input: toolInput,
        result: `执行失败: ${error}`,
        success: false
      };
      setExecutionHistory(prev => [newExecution, ...prev.slice(0, 9)]);
    } finally {
      setLoading(false);
    }
  };

  // 执行工具链
  const executeToolChain = (chain: ToolChain) => {
    const command = `#tool:toolChain ${chain.triggers[0]}`;
    onSendMessage(command);
  };

  useEffect(() => {
    fetchMCPStatus();
    fetchToolChains();
  }, []);

  const getStatusIcon = (connected: boolean) => {
    return connected ? '✅' : '❌';
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'web': return '🌐';
      case 'file': return '📁';
      case 'time': return '⏰';
      default: return '⚡';
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">MCP工具管理中心</h1>
        <p className="text-gray-600">管理和监控所有MCP服务器和工具链</p>
      </div>

      {/* 简化的标签页 */}
      <div className="mb-6">
        <div className="flex space-x-4 border-b border-gray-200">
          {['servers', 'toolchains', 'executor', 'history'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab === 'servers' && 'MCP服务器'}
              {tab === 'toolchains' && '工具链'}
              {tab === 'executor' && '工具执行器'}
              {tab === 'history' && '执行历史'}
            </button>
          ))}
        </div>
      </div>

      {/* MCP服务器标签页 */}
      {activeTab === 'servers' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {mcpServers.map((server) => (
            <Card key={server.name} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{server.name}</CardTitle>
                  <span className="text-lg">{getStatusIcon(server.connected)}</span>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">{server.description}</p>
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="secondary">
                    {server.toolCount} 工具
                  </Badge>
                  <Badge variant={server.connected ? "default" : "destructive"}>
                    {server.connected ? "已连接" : "未连接"}
                  </Badge>
                </div>
                <div>
                  <p className="text-xs text-gray-500 mb-1">可用工具:</p>
                  <div className="flex flex-wrap gap-1">
                    {server.tools.slice(0, 3).map((tool) => (
                      <Badge key={tool} variant="outline" className="text-xs">
                        {tool}
                      </Badge>
                    ))}
                    {server.tools.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{server.tools.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 工具链标签页 */}
      {activeTab === 'toolchains' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {toolChains.map((chain) => (
            <Card key={chain.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span>{getCategoryIcon(chain.category)}</span>
                    <CardTitle className="text-lg">{chain.name}</CardTitle>
                  </div>
                  <Badge variant="outline">{chain.category}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">{chain.description}</p>

                <div className="mb-3">
                  <p className="text-xs text-gray-500 mb-1">触发词:</p>
                  <div className="flex flex-wrap gap-1">
                    {chain.triggers.map((trigger) => (
                      <Badge key={trigger} variant="secondary" className="text-xs">
                        {trigger}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-xs text-gray-500 mb-1">执行步骤 ({chain.steps.length}):</p>
                  <div className="space-y-1">
                    {chain.steps.slice(0, 2).map((step, index) => (
                      <div key={index} className="text-xs bg-gray-50 p-2 rounded">
                        {index + 1}. {step.toolName}
                      </div>
                    ))}
                    {chain.steps.length > 2 && (
                      <div className="text-xs text-gray-500">
                        ... 还有 {chain.steps.length - 2} 个步骤
                      </div>
                    )}
                  </div>
                </div>

                <Button
                  onClick={() => executeToolChain(chain)}
                  className="w-full"
                  size="sm"
                >
                  ▶️ 执行工具链
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 工具执行器标签页 */}
      {activeTab === 'executor' && (
        <Card>
          <CardHeader>
            <CardTitle>工具执行器</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">选择工具</label>
              <select
                value={selectedTool}
                onChange={(e) => setSelectedTool(e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="">请选择工具...</option>
                <option value="mcpStatus">MCP状态</option>
                <option value="mcpTime">时间工具</option>
                <option value="mcpFetch">网络获取</option>
                <option value="mcpFileSystem">文件系统</option>
                <option value="mcpMemory">记忆系统</option>
                <option value="mcpPuppeteer">浏览器自动化</option>
                <option value="mcpEverything">演示工具</option>
                <option value="toolChain">工具链执行</option>
                <option value="toolChainManager">工具链管理</option>
                <option value="taskDecomposer">任务分解</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">工具输入</label>
              <textarea
                value={toolInput}
                onChange={(e) => setToolInput(e.target.value)}
                placeholder="输入工具参数..."
                rows={3}
                className="w-full p-2 border rounded-md"
              />
            </div>

            <Button
              onClick={executeTool}
              disabled={!selectedTool || !toolInput || loading}
              className="w-full"
            >
              {loading ? '⏳ 执行中...' : '▶️ 执行工具'}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* 执行历史标签页 */}
      {activeTab === 'history' && (
        <Card>
          <CardHeader>
            <CardTitle>执行历史</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {executionHistory.length === 0 ? (
                <p className="text-gray-500 text-center py-8">暂无执行历史</p>
              ) : (
                executionHistory.map((execution, index) => (
                  <div key={index} className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span>{execution.success ? '✅' : '❌'}</span>
                        <span className="font-medium">{execution.tool}</span>
                      </div>
                      <span className="text-xs text-gray-500">{execution.timestamp}</span>
                    </div>
                    <div className="text-sm text-gray-600 mb-1">
                      输入: {execution.input}
                    </div>
                    <div className="text-sm">
                      结果: {execution.result}
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ToolManager;
