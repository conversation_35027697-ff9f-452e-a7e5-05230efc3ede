/**
 * AI智能体实现，支持真实的AI模型推理
 */
import { Agent, AgentInput, AgentOutput, AgentMemory } from '../common/agent';
import { ToolPlugin } from '../common/plugin';
import { OfficialMCPClient, MCPToolDiscovery } from '../mcp/officialClient';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

interface ModelConfig {
  model: string;
  apiKey?: string;
  baseUrl?: string;
}

export class AIAgent implements Agent {
  id: string;
  name: string;
  memory: AgentMemory;
  tools: ToolPlugin[];
  modelConfig: ModelConfig;
  private mcpClient: OfficialMCPClient | null = null;
  private mcpDiscovery: MCPToolDiscovery | null = null;
  private mcpEnabled: boolean = false;

  constructor(id: string, name: string, memory: AgentMemory, tools: ToolPlugin[] = [], modelConfig: ModelConfig) {
    this.id = id;
    this.name = name;
    this.memory = memory;
    this.tools = tools;
    this.modelConfig = modelConfig;

    // 初始化MCP客户端（如果启用）
    this.initializeMCP();
  }

  /**
   * 初始化MCP客户端
   */
  private async initializeMCP(): Promise<void> {
    try {
      const config = this.getConfig();
      if (config.mcp?.enabled && config.mcp?.serverUrl) {
        this.mcpClient = new OfficialMCPClient();
        await this.mcpClient.connect(config.mcp.serverUrl);
        this.mcpDiscovery = new MCPToolDiscovery(this.mcpClient);

        // 自动发现工具
        await this.mcpDiscovery.discoverTools();
        this.mcpEnabled = true;

        console.log('MCP客户端初始化成功，已连接到:', config.mcp.serverUrl);
      }
    } catch (error) {
      console.error('MCP客户端初始化失败:', error);
      this.mcpEnabled = false;
    }
  }

  /**
   * 智能体主推理方法
   */
  async act(input: AgentInput): Promise<AgentOutput> {
    // 记忆存储
    await this.memory.remember(`input:${Date.now()}`, input);
    
    let content = input.content;
    let toolCalls: string[] = [];

    // 1. 首先处理工具调用
    const { processedContent, executedTools } = await this.processToolCalls(content, input);
    content = processedContent;
    toolCalls = executedTools;

    // 2. 如果没有工具调用，或者需要AI推理，调用AI模型
    if (toolCalls.length === 0 || this.needsAIResponse(content)) {
      const aiResponse = await this.callAIModel(content, input);
      if (aiResponse) {
        content = aiResponse;
      }
    }

    // 记忆输出
    await this.memory.remember(`output:${Date.now()}`, content);
    return { content, toolCalls };
  }

  /**
   * 处理工具调用（支持本地工具和MCP工具）
   */
  private async processToolCalls(content: string, input: AgentInput): Promise<{ processedContent: string, executedTools: string[] }> {
    let processedContent = content;
    const executedTools: string[] = [];

    // 使用正则表达式匹配所有工具调用
    const toolPattern = /#tool:(\w+)([^#]*?)(?=#tool:|$)/g;
    const matches = [...content.matchAll(toolPattern)];

    for (const match of matches) {
      const toolName = match[1];
      const toolInput = match[2].trim();

      // 首先尝试MCP工具
      if (this.mcpEnabled && this.mcpDiscovery?.hasToolAvailable(toolName)) {
        try {
          const result = await this.mcpClient!.callTool({
            name: toolName,
            arguments: this.parseToolArguments(toolName, toolInput)
          });

          const resultText = result.content
            .filter(c => c.type === 'text')
            .map(c => c.text)
            .join('\n');

          processedContent += `\n[MCP工具${toolName}结果]: ${resultText}`;
          executedTools.push(toolName);
          continue;
        } catch (error) {
          processedContent += `\n[MCP工具${toolName}错误]: ${error instanceof Error ? error.message : '未知错误'}`;
          continue;
        }
      }

      // 然后尝试本地工具
      const tool = this.tools.find(t => t.name === toolName);
      if (tool) {
        try {
          const result = await tool.run(toolInput, { userId: input.userId, agentId: this.id, memory: this.memory });
          processedContent += `\n[工具${tool.name}结果]: ${result}`;
          executedTools.push(tool.name);
        } catch (error) {
          processedContent += `\n[工具${tool.name}错误]: ${error instanceof Error ? error.message : '未知错误'}`;
        }
      } else {
        processedContent += `\n[错误]: 未找到工具 ${toolName}`;
      }
    }

    return { processedContent, executedTools };
  }

  /**
   * 解析工具参数
   */
  private parseToolArguments(toolName: string, toolInput: string): Record<string, any> {
    const trimmedInput = toolInput.trim();

    switch (toolName) {
      case 'math':
        return { expression: trimmedInput };
      case 'weather':
        return { city: trimmedInput };
      case 'webSearch':
        return { query: trimmedInput };
      case 'getTime':
        return {};
      case 'browserNavigate':
        return { url: trimmedInput };
      case 'browserClick':
        return { selector: trimmedInput };
      case 'browserType':
        // 解析 "selector text" 格式
        const parts = trimmedInput.split(' ');
        if (parts.length >= 2) {
          return {
            selector: parts[0],
            text: parts.slice(1).join(' ')
          };
        }
        return { selector: trimmedInput, text: '' };
      case 'browserExtract':
        return { selector: trimmedInput };
      case 'browserScreenshot':
        return { filename: trimmedInput || undefined };
      case 'browserAutomate':
        // 解析 "task [url]" 格式
        const automateparts = trimmedInput.split(' ');
        if (automateparts.length >= 2 && automateparts[1].includes('.')) {
          return {
            task: automateparts[0],
            url: automateparts[1]
          };
        }
        return { task: trimmedInput };
      default:
        // 尝试解析为JSON，如果失败则作为单个参数
        try {
          return JSON.parse(trimmedInput);
        } catch {
          return { input: trimmedInput };
        }
    }
  }

  /**
   * 判断是否需要AI响应
   */
  private needsAIResponse(content: string): boolean {
    // 如果内容不只是工具调用，需要AI响应
    const hasNonToolContent = content.replace(/#tool:\w+[^#]*?(?=#tool:|$)/g, '').trim().length > 0;
    return hasNonToolContent;
  }

  /**
   * 调用AI模型
   */
  private async callAIModel(content: string, input: AgentInput): Promise<string | null> {
    try {
      // 构建包含工具信息的提示词
      const systemPrompt = this.buildSystemPrompt();
      const userPrompt = content;

      switch (this.modelConfig.model) {
        case 'gpt-4':
        case 'gpt-3.5-turbo':
          return await this.callOpenAI(userPrompt, input, systemPrompt);
        case 'claude-3':
          return await this.callClaude(userPrompt, input, systemPrompt);
        case 'llama3.2:3b':
          return await this.callOllama(userPrompt, input, systemPrompt);
        case 'custom-agent':
          return await this.callCustomAgent(userPrompt, input);
        default:
          return `当前模型 ${this.modelConfig.model} 暂未实现，请在设置中选择其他模型。`;
      }
    } catch (error) {
      console.error('AI model call failed:', error);
      return `AI模型调用失败: ${error instanceof Error ? error.message : '未知错误'}`;
    }
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(): string {
    const toolsInfo = this.tools.map(tool =>
      `- ${tool.name}: ${tool.description}`
    ).join('\n');

    const customPrompt = this.getCustomPrompt();

    return `${customPrompt}

可用工具：
${toolsInfo}

工具调用格式：当你需要使用工具时，请在回复中包含 #tool:工具名 参数 的格式。
例如：
- 获取时间：#tool:getTime
- 数学计算：#tool:math 10 + 5
- 天气查询：#tool:weather 北京
- 网络搜索：#tool:webSearch 人工智能

你可以在回复中自然地包含工具调用，我会执行这些工具并将结果返回给你。`;
  }

  /**
   * 获取自定义提示词
   */
  private getCustomPrompt(): string {
    try {
      const configPath = path.join(process.cwd(), 'config', 'system.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      return config.systemPrompt || '你是一个有用的AI助手，可以使用各种工具来帮助用户。';
    } catch {
      return '你是一个有用的AI助手，可以使用各种工具来帮助用户。';
    }
  }

  /**
   * 调用OpenAI API
   */
  private async callOpenAI(content: string, input: AgentInput, systemPrompt: string): Promise<string> {
    const apiKey = this.getApiKey('openai');
    if (!apiKey) {
      return `请在设置中配置OpenAI API密钥以使用 ${this.modelConfig.model} 模型。`;
    }

    const response = await axios.post('https://api.openai.com/v1/chat/completions', {
      model: this.modelConfig.model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: content }
      ],
      max_tokens: 1500,
      temperature: 0.7
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    const aiResponse = (response.data as any).choices[0].message.content;

    // 处理AI模型的工具调用
    return await this.processAIToolCalls(aiResponse, input);
  }

  /**
   * 调用Claude API
   */
  private async callClaude(content: string, input: AgentInput, systemPrompt: string): Promise<string> {
    const apiKey = this.getApiKey('anthropic');
    if (!apiKey) {
      return `请在设置中配置Anthropic API密钥以使用Claude模型。`;
    }

    // Claude API调用实现（需要实际的API调用）
    const aiResponse = `Claude模型响应: ${content}（需要配置Anthropic API密钥）`;
    return await this.processAIToolCalls(aiResponse, input);
  }

  /**
   * 调用Ollama本地模型
   */
  private async callOllama(content: string, input: AgentInput, systemPrompt: string): Promise<string> {
    try {
      const fullPrompt = `${systemPrompt}\n\n用户: ${content}\n\n助手:`;

      const response = await axios.post('http://localhost:11434/api/generate', {
        model: 'llama3.2:3b',
        prompt: fullPrompt,
        stream: false
      }, {
        timeout: 30000
      });

      const aiResponse = (response.data as any).response;
      return await this.processAIToolCalls(aiResponse, input);
    } catch (error) {
      return `本地Ollama模型未运行。请先安装并启动Ollama，然后运行: ollama pull llama3.2:3b`;
    }
  }

  /**
   * 调用自定义智能体
   */
  private async callCustomAgent(content: string, input: AgentInput): Promise<string> {
    // 简单的规则基础响应
    if (content.includes('你好') || content.includes('hello')) {
      return `你好！我是${this.name}，很高兴为您服务。您可以使用以下工具：${this.tools.map(t => t.name).join(', ')}`;
    }
    
    if (content.includes('帮助') || content.includes('help')) {
      return `我可以帮您：\n1. 获取时间 (#tool:getTime)\n2. 数学计算 (#tool:math)\n3. 天气查询 (#tool:weather)\n4. 网络搜索 (#tool:webSearch)`;
    }

    return `我收到了您的消息："${content}"。作为自定义智能体，我可以帮您使用各种工具。请尝试使用 #tool:工具名 的格式调用工具，或者说"帮助"获取更多信息。`;
  }

  /**
   * 处理AI模型回复中的工具调用
   */
  private async processAIToolCalls(aiResponse: string, input: AgentInput): Promise<string> {
    let processedResponse = aiResponse;

    // 检查AI回复中是否包含工具调用
    const toolPattern = /#tool:(\w+)([^#]*?)(?=#tool:|$)/g;
    const matches = [...aiResponse.matchAll(toolPattern)];

    if (matches.length > 0) {
      // 执行工具调用
      for (const match of matches) {
        const toolName = match[1];
        const toolInput = match[2].trim();

        const tool = this.tools.find(t => t.name === toolName);
        if (tool) {
          try {
            const result = await tool.run(toolInput, { userId: input.userId, agentId: this.id, memory: this.memory });
            // 替换工具调用为结果
            const toolCall = match[0];
            processedResponse = processedResponse.replace(toolCall, `[工具${tool.name}结果]: ${result}`);
          } catch (error) {
            const toolCall = match[0];
            processedResponse = processedResponse.replace(toolCall, `[工具${tool.name}错误]: ${error instanceof Error ? error.message : '未知错误'}`);
          }
        }
      }
    }

    return processedResponse;
  }

  /**
   * 获取API密钥
   */
  private getApiKey(provider: string): string | null {
    try {
      const configPath = path.join(process.cwd(), 'config', 'system.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);

      switch (provider) {
        case 'openai':
          return config.apiKeys?.openai || process.env.OPENAI_API_KEY || null;
        case 'anthropic':
          return config.apiKeys?.anthropic || process.env.ANTHROPIC_API_KEY || null;
        default:
          return null;
      }
    } catch {
      return null;
    }
  }

  /**
   * 获取配置
   */
  private getConfig(): any {
    try {
      const configPath = path.join(process.cwd(), 'config', 'system.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      return JSON.parse(configData);
    } catch {
      return {
        mcp: { enabled: false, serverUrl: 'http://localhost:3002' }
      };
    }
  }

  /**
   * 获取可用工具列表（包括MCP工具）
   */
  async getAvailableTools(): Promise<string[]> {
    const tools = this.tools.map(t => t.name);

    if (this.mcpEnabled && this.mcpDiscovery) {
      const mcpTools = this.mcpDiscovery.getToolNames();
      tools.push(...mcpTools);
    }

    return [...new Set(tools)]; // 去重
  }

  /**
   * 重新初始化MCP连接
   */
  async reinitializeMCP(): Promise<void> {
    if (this.mcpClient) {
      await this.mcpClient.disconnect();
    }
    await this.initializeMCP();
  }
}
