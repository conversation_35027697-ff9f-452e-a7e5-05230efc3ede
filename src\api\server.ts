import Fastify, { FastifyRequest, FastifyReply } from 'fastify';
import cors from '@fastify/cors';
import { createAgent } from '../common/factory';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';

interface ActRequest {
  userId: string;
  content: string;
}

interface ConfigRequest {
  model: string;
  memory: {
    type: string;
  };
  plugins: string[];
  apiKeys: {
    openweather?: string;
    google?: string;
  };
}

interface ModelInfo {
  id: string;
  name: string;
  description: string;
  type: 'local' | 'cloud';
  size?: string;
  modified?: string;
  available: boolean;
}

/**
 * 获取可用的AI模型列表
 */
async function getAvailableModels(): Promise<ModelInfo[]> {
  const models: ModelInfo[] = [];

  // 云端模型（始终可用，但需要API密钥）
  models.push(
    {
      id: 'gpt-4',
      name: 'GPT-4',
      description: 'OpenAI最强大的模型，适合复杂任务',
      type: 'cloud',
      available: true
    },
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      description: 'OpenAI经济型模型，响应快速',
      type: 'cloud',
      available: true
    },
    {
      id: 'claude-3',
      name: 'Claude 3',
      description: 'Anthropic的强大模型，擅长分析和推理',
      type: 'cloud',
      available: true
    },
    {
      id: 'custom-agent',
      name: '自定义智能体',
      description: '本地实现的基础智能体',
      type: 'local',
      available: true
    }
  );

  // 获取Ollama本地模型
  try {
    const response = await axios.get('http://localhost:11434/api/tags', {
      timeout: 5000
    });

    if (response.data && (response.data as any).models) {
      for (const model of (response.data as any).models) {
        models.push({
          id: (model as any).name,
          name: (model as any).name,
          description: `本地Ollama模型 - ${(model as any).name}`,
          type: 'local',
          size: formatBytes((model as any).size || 0),
          modified: (model as any).modified_at ? new Date((model as any).modified_at).toLocaleDateString() : undefined,
          available: true
        });
      }
    }
  } catch (error) {
    console.log('Ollama not available or no models installed');

    // 添加常见的Ollama模型作为可安装选项
    const commonOllamaModels = [
      { id: 'llama3.2:3b', name: 'Llama 3.2 3B', description: '轻量级本地模型，适合日常对话' },
      { id: 'llama3.2:1b', name: 'Llama 3.2 1B', description: '超轻量级模型，极快响应' },
      { id: 'llama3.1:8b', name: 'Llama 3.1 8B', description: '中等规模模型，平衡性能和质量' },
      { id: 'qwen2.5:7b', name: 'Qwen 2.5 7B', description: '阿里巴巴开源模型，中文优化' },
      { id: 'gemma2:9b', name: 'Gemma 2 9B', description: 'Google开源模型，高质量输出' },
      { id: 'mistral:7b', name: 'Mistral 7B', description: '欧洲开源模型，多语言支持' },
      { id: 'codellama:7b', name: 'Code Llama 7B', description: '专门用于代码生成的模型' }
    ];

    for (const model of commonOllamaModels) {
      models.push({
        ...model,
        type: 'local',
        available: false // 标记为未安装
      });
    }
  }

  return models;
}

/**
 * 格式化字节大小
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

(async () => {
  const fastify = Fastify();
  await fastify.register(cors, { origin: true });

  // 动态创建智能体的函数
  let currentAgent = createAgent('agent-web', 'Web智能体');

  // 重新创建智能体的函数
  const recreateAgent = (model?: string) => {
    currentAgent = createAgent('agent-web', 'Web智能体', model);
  };

  fastify.post('/api/agent/act', async (req: FastifyRequest<{ Body: ActRequest }>, reply: FastifyReply) => {
    const { userId, content } = req.body;
    const output = await currentAgent.act({ userId, content });
    reply.send({ role: 'agent', content: output.content });
  });

  fastify.get('/api/agent/history', async (req: FastifyRequest, reply: FastifyReply) => {
    const history = await currentAgent.memory.getHistory(20);
    // 返回格式化消息
    const msgs = history.map((h: string) => {
      try {
        const obj = JSON.parse(h);
        return { role: 'user', content: obj.content, timestamp: new Date() };
      } catch {
        return { role: 'agent', content: h, timestamp: new Date() };
      }
    });
    reply.send(msgs);
  });

  // 清空聊天记录
  fastify.delete('/api/agent/history', async (req: FastifyRequest, reply: FastifyReply) => {
    // 这里可以实现清空记忆的逻辑
    // 由于当前使用的是内存存储，重启服务器就会清空
    reply.send({ success: true, message: '聊天记录已清空' });
  });

  // 获取可用插件列表
  fastify.get('/api/agent/plugins', async (req: FastifyRequest, reply: FastifyReply) => {
    const plugins = [
      { name: 'getTime', description: '获取当前时间' },
      { name: 'webSearch', description: '网络搜索功能' },
      { name: 'math', description: '数学计算功能' },
      { name: 'weather', description: '天气查询功能' },
    ];
    reply.send(plugins);
  });

  // 获取系统配置
  fastify.get('/api/config', async (req: FastifyRequest, reply: FastifyReply) => {
    try {
      const configPath = path.join(process.cwd(), 'config', 'system.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      reply.send(config);
    } catch (error) {
      reply.status(500).send({ error: 'Failed to load config' });
    }
  });

  // 获取可用的本地模型
  fastify.get('/api/models', async (req: FastifyRequest, reply: FastifyReply) => {
    try {
      const models = await getAvailableModels();
      reply.send({ models });
    } catch (error) {
      console.error('Failed to get models:', error);
      reply.status(500).send({ error: 'Failed to get models' });
    }
  });

  // 安装Ollama模型
  fastify.post('/api/models/install', async (req: FastifyRequest<{ Body: { modelId: string } }>, reply: FastifyReply) => {
    try {
      const { modelId } = req.body;

      if (!modelId) {
        reply.status(400).send({ error: 'Model ID is required' });
        return;
      }

      // 调用Ollama API拉取模型
      const response = await axios.post('http://localhost:11434/api/pull', {
        name: modelId,
        stream: false
      }, {
        timeout: 300000 // 5分钟超时，因为下载模型可能需要很长时间
      });

      if (response.status === 200) {
        reply.send({ success: true, message: `模型 ${modelId} 安装成功` });
      } else {
        reply.status(500).send({ error: `模型 ${modelId} 安装失败` });
      }
    } catch (error) {
      console.error('Failed to install model:', error);
      if (error && typeof error === 'object' && 'code' in error && (error as any).code === 'ECONNREFUSED') {
        reply.status(503).send({ error: 'Ollama服务未运行，请先启动Ollama' });
      } else {
        reply.status(500).send({ error: '模型安装失败' });
      }
    }
  });

  // 更新系统配置
  fastify.post('/api/config', async (req: FastifyRequest<{ Body: ConfigRequest }>, reply: FastifyReply) => {
    try {
      const configPath = path.join(process.cwd(), 'config', 'system.json');
      const newConfig = req.body;

      // 验证配置
      if (!newConfig.model || !newConfig.plugins) {
        reply.status(400).send({ error: 'Invalid config format' });
        return;
      }

      // 保存配置
      fs.writeFileSync(configPath, JSON.stringify(newConfig, null, 2));

      // 重新创建智能体以应用新配置
      recreateAgent(newConfig.model);

      reply.send({ success: true, message: '配置已保存，模型已切换' });
    } catch (error) {
      reply.status(500).send({ error: 'Failed to save config' });
    }
  });

  fastify.listen({ port: 3001, host: '0.0.0.0' }, (err: Error | null) => {
    if (err) throw err;
    console.log('API server running on http://localhost:3001');
  });
})();
