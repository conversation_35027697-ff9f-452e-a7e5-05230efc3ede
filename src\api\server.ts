import Fastify, { FastifyRequest, FastifyReply } from 'fastify';
import cors from '@fastify/cors';
import { createAgent } from '../common/factory';
import * as fs from 'fs';
import * as path from 'path';

interface ActRequest {
  userId: string;
  content: string;
}

interface ConfigRequest {
  model: string;
  memory: {
    type: string;
  };
  plugins: string[];
  apiKeys: {
    openweather?: string;
    google?: string;
  };
}

(async () => {
  const fastify = Fastify();
  await fastify.register(cors, { origin: true });

  // 动态创建智能体的函数
  let currentAgent = createAgent('agent-web', 'Web智能体');

  // 重新创建智能体的函数
  const recreateAgent = (model?: string) => {
    currentAgent = createAgent('agent-web', 'Web智能体', model);
  };

  fastify.post('/api/agent/act', async (req: FastifyRequest<{ Body: ActRequest }>, reply: FastifyReply) => {
    const { userId, content } = req.body;
    const output = await currentAgent.act({ userId, content });
    reply.send({ role: 'agent', content: output.content });
  });

  fastify.get('/api/agent/history', async (req: FastifyRequest, reply: FastifyReply) => {
    const history = await currentAgent.memory.getHistory(20);
    // 返回格式化消息
    const msgs = history.map((h: string) => {
      try {
        const obj = JSON.parse(h);
        return { role: 'user', content: obj.content, timestamp: new Date() };
      } catch {
        return { role: 'agent', content: h, timestamp: new Date() };
      }
    });
    reply.send(msgs);
  });

  // 清空聊天记录
  fastify.delete('/api/agent/history', async (req: FastifyRequest, reply: FastifyReply) => {
    // 这里可以实现清空记忆的逻辑
    // 由于当前使用的是内存存储，重启服务器就会清空
    reply.send({ success: true, message: '聊天记录已清空' });
  });

  // 获取可用插件列表
  fastify.get('/api/agent/plugins', async (req: FastifyRequest, reply: FastifyReply) => {
    const plugins = [
      { name: 'getTime', description: '获取当前时间' },
      { name: 'webSearch', description: '网络搜索功能' },
      { name: 'math', description: '数学计算功能' },
      { name: 'weather', description: '天气查询功能' },
    ];
    reply.send(plugins);
  });

  // 获取系统配置
  fastify.get('/api/config', async (req: FastifyRequest, reply: FastifyReply) => {
    try {
      const configPath = path.join(process.cwd(), 'config', 'system.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);
      reply.send(config);
    } catch (error) {
      reply.status(500).send({ error: 'Failed to load config' });
    }
  });

  // 更新系统配置
  fastify.post('/api/config', async (req: FastifyRequest<{ Body: ConfigRequest }>, reply: FastifyReply) => {
    try {
      const configPath = path.join(process.cwd(), 'config', 'system.json');
      const newConfig = req.body;

      // 验证配置
      if (!newConfig.model || !newConfig.plugins) {
        reply.status(400).send({ error: 'Invalid config format' });
        return;
      }

      // 保存配置
      fs.writeFileSync(configPath, JSON.stringify(newConfig, null, 2));

      // 重新创建智能体以应用新配置
      recreateAgent(newConfig.model);

      reply.send({ success: true, message: '配置已保存，模型已切换' });
    } catch (error) {
      reply.status(500).send({ error: 'Failed to save config' });
    }
  });

  fastify.listen({ port: 3001, host: '0.0.0.0' }, (err: Error | null) => {
    if (err) throw err;
    console.log('API server running on http://localhost:3001');
  });
})();
