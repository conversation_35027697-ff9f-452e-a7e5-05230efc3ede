import Fastify from 'fastify';
import cors from '@fastify/cors';
import { createAgent } from '../common/factory';

(async () => {
  const fastify = Fastify();
  await fastify.register(cors, { origin: true });

  const agent = createAgent('agent-web', 'Web智能体');

  fastify.post('/api/agent/act', async (req, reply) => {
    const { userId, content } = req.body as { userId: string; content: string };
    const output = await agent.act({ userId, content });
    reply.send({ role: 'agent', content: output.content });
  });

  fastify.get('/api/agent/history', async (req, reply) => {
    const history = await agent.memory.getHistory(20);
    // 返回格式化消息
    const msgs = history.map((h: string) => {
      try {
        const obj = JSON.parse(h);
        return { role: 'user', content: obj.content };
      } catch {
        return { role: 'agent', content: h };
      }
    });
    reply.send(msgs);
  });

  fastify.listen({ port: 3001, host: '0.0.0.0' }, err => {
    if (err) throw err;
    console.log('API server running on http://localhost:3001');
  });
})();
