/**
 * 终端命令执行插件 - 安全的命令行工具
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import { spawn, exec } from 'child_process';
import * as os from 'os';
import * as path from 'path';

// 安全配置
const TERMINAL_CONFIG = {
  // 命令执行超时时间（毫秒）
  timeout: 30000,
  
  // 允许的安全命令白名单
  allowedCommands: [
    // 基础系统命令
    'ls', 'dir', 'pwd', 'cd', 'echo', 'cat', 'type', 'head', 'tail',
    'find', 'grep', 'wc', 'sort', 'uniq', 'cut', 'awk', 'sed',
    
    // 文件操作
    'mkdir', 'rmdir', 'touch', 'cp', 'copy', 'mv', 'move',
    
    // 系统信息
    'whoami', 'date', 'uptime', 'ps', 'top', 'df', 'du', 'free',
    'uname', 'hostname', 'which', 'where',
    
    // 网络工具
    'ping', 'curl', 'wget', 'nslookup', 'dig',
    
    // 开发工具
    'node', 'npm', 'npx', 'git', 'python', 'pip', 'java', 'javac',
    'gcc', 'make', 'cmake', 'docker', 'kubectl',
    
    // 文本处理
    'less', 'more', 'vim', 'nano', 'code',
    
    // 压缩工具
    'zip', 'unzip', 'tar', 'gzip', 'gunzip'
  ],
  
  // 危险命令黑名单
  blockedCommands: [
    // 系统危险命令
    'rm', 'del', 'format', 'fdisk', 'mkfs', 'dd',
    'shutdown', 'reboot', 'halt', 'poweroff',
    
    // 权限相关
    'sudo', 'su', 'chmod', 'chown', 'passwd',
    
    // 网络危险命令
    'iptables', 'netsh', 'route',
    
    // 进程控制
    'kill', 'killall', 'pkill', 'taskkill',
    
    // 系统修改
    'crontab', 'systemctl', 'service', 'chkconfig'
  ],
  
  // 工作目录限制（相对于项目根目录）
  allowedDirectories: [
    './workspace',
    './temp',
    './output',
    './logs'
  ]
};

/**
 * 检查命令是否安全
 */
function isCommandSafe(command: string): { safe: boolean; reason?: string } {
  const cmd = command.trim().split(' ')[0].toLowerCase();
  
  // 检查黑名单
  if (TERMINAL_CONFIG.blockedCommands.includes(cmd)) {
    return { safe: false, reason: `命令 '${cmd}' 被安全策略禁止` };
  }
  
  // 检查白名单
  if (!TERMINAL_CONFIG.allowedCommands.includes(cmd)) {
    return { safe: false, reason: `命令 '${cmd}' 不在允许的命令列表中` };
  }
  
  // 检查危险参数
  const dangerousPatterns = [
    /--force/i,
    /-f\s/,
    /--recursive/i,
    /-r\s/,
    />\s*\/dev\/null/,
    /&&/,
    /\|\|/,
    /;/,
    /`/,
    /\$\(/
  ];
  
  for (const pattern of dangerousPatterns) {
    if (pattern.test(command)) {
      return { safe: false, reason: `命令包含潜在危险的参数或操作符` };
    }
  }
  
  return { safe: true };
}

/**
 * 获取安全的工作目录
 */
function getSafeWorkingDirectory(requestedPath?: string): string {
  const projectRoot = process.cwd();
  
  if (!requestedPath) {
    return path.join(projectRoot, 'workspace');
  }
  
  // 确保路径在允许的目录内
  const resolvedPath = path.resolve(projectRoot, requestedPath);
  
  for (const allowedDir of TERMINAL_CONFIG.allowedDirectories) {
    const allowedPath = path.resolve(projectRoot, allowedDir);
    if (resolvedPath.startsWith(allowedPath)) {
      return resolvedPath;
    }
  }
  
  // 如果不在允许的目录内，返回默认工作目录
  return path.join(projectRoot, 'workspace');
}

/**
 * 执行命令的Promise包装
 */
function executeCommand(command: string, workingDir: string): Promise<{
  stdout: string;
  stderr: string;
  exitCode: number;
  duration: number;
}> {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const child = exec(command, {
      cwd: workingDir,
      timeout: TERMINAL_CONFIG.timeout,
      maxBuffer: 1024 * 1024, // 1MB buffer
      env: { ...process.env, PATH: process.env.PATH }
    }, (error, stdout, stderr) => {
      const duration = Date.now() - startTime;
      
      if (error) {
        if ((error as any).code === 'ETIMEDOUT') {
          reject(new Error(`命令执行超时 (${TERMINAL_CONFIG.timeout}ms)`));
        } else {
          resolve({
            stdout: stdout || '',
            stderr: stderr || error.message,
            exitCode: (error as any).code || 1,
            duration
          });
        }
      } else {
        resolve({
          stdout: stdout || '',
          stderr: stderr || '',
          exitCode: 0,
          duration
        });
      }
    });
    
    // 设置超时处理
    setTimeout(() => {
      child.kill('SIGTERM');
    }, TERMINAL_CONFIG.timeout);
  });
}

/**
 * 终端命令执行工具
 */
export const terminalTool: ToolPlugin = {
  name: 'terminal',
  description: '安全的终端命令执行工具，支持常用的系统命令和开发工具',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      // 解析输入
      const lines = input.trim().split('\n');
      let command = '';
      let workingDir = '';
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('cd ') || trimmedLine.startsWith('工作目录:')) {
          workingDir = trimmedLine.replace(/^(cd |工作目录:)\s*/, '');
        } else if (trimmedLine && !trimmedLine.startsWith('#')) {
          command = trimmedLine;
          break;
        }
      }
      
      if (!command) {
        return `❌ 请提供要执行的命令

📖 使用方法:
- 直接输入命令: ls -la
- 指定工作目录: cd ./workspace
- 多行输入支持

🛡️ 安全限制:
- 只允许安全的命令执行
- 工作目录限制在项目范围内
- 命令执行超时: ${TERMINAL_CONFIG.timeout / 1000}秒

💡 可用命令示例:
- ls / dir (列出文件)
- pwd (当前目录)
- echo "hello" (输出文本)
- node --version (检查Node版本)
- git status (Git状态)`;
      }
      
      // 安全检查
      const safetyCheck = isCommandSafe(command);
      if (!safetyCheck.safe) {
        return `🚫 安全检查失败: ${safetyCheck.reason}

✅ 允许的命令类型:
- 文件操作: ls, cat, mkdir, cp, mv
- 系统信息: pwd, whoami, date, ps
- 开发工具: node, npm, git, python
- 网络工具: ping, curl
- 文本处理: grep, sort, head, tail

❌ 禁止的命令:
- 删除操作: rm, del
- 系统控制: shutdown, reboot
- 权限修改: sudo, chmod
- 进程控制: kill, killall`;
      }
      
      // 获取安全的工作目录
      const safeWorkingDir = getSafeWorkingDirectory(workingDir);
      
      // 执行命令
      const result = await executeCommand(command, safeWorkingDir);
      
      // 格式化输出
      let output = `🖥️ 终端命令执行结果\n\n`;
      output += `📂 工作目录: ${safeWorkingDir}\n`;
      output += `⚡ 命令: ${command}\n`;
      output += `⏱️ 执行时间: ${result.duration}ms\n`;
      output += `🔢 退出代码: ${result.exitCode}\n\n`;
      
      if (result.stdout) {
        output += `📤 标准输出:\n\`\`\`\n${result.stdout}\n\`\`\`\n\n`;
      }
      
      if (result.stderr) {
        output += `⚠️ 错误输出:\n\`\`\`\n${result.stderr}\n\`\`\`\n\n`;
      }
      
      if (!result.stdout && !result.stderr) {
        output += `✅ 命令执行完成，无输出内容\n`;
      }
      
      // 添加状态指示
      if (result.exitCode === 0) {
        output += `✅ 命令执行成功`;
      } else {
        output += `❌ 命令执行失败 (退出代码: ${result.exitCode})`;
      }
      
      return output;
      
    } catch (error: any) {
      console.error('终端命令执行失败:', error);
      return `❌ 终端命令执行失败: ${error?.message || error}

🔧 可能的解决方案:
1. 检查命令语法是否正确
2. 确认命令在当前系统中可用
3. 检查工作目录是否存在
4. 确认命令执行权限

💡 提示: 使用 'which 命令名' 或 'where 命令名' 检查命令是否可用`;
    }
  }
};

/**
 * 终端信息查询工具
 */
export const terminalInfoTool: ToolPlugin = {
  name: 'terminalInfo',
  description: '获取终端和系统信息',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const platform = os.platform();
      const arch = os.arch();
      const release = os.release();
      const hostname = os.hostname();
      const uptime = os.uptime();
      const totalMem = os.totalmem();
      const freeMem = os.freemem();
      const cpus = os.cpus();
      
      let output = `💻 系统信息\n\n`;
      output += `🖥️ 操作系统: ${platform} ${arch}\n`;
      output += `📋 系统版本: ${release}\n`;
      output += `🏠 主机名: ${hostname}\n`;
      output += `⏰ 运行时间: ${Math.floor(uptime / 3600)}小时 ${Math.floor((uptime % 3600) / 60)}分钟\n`;
      output += `💾 内存使用: ${((totalMem - freeMem) / 1024 / 1024 / 1024).toFixed(2)}GB / ${(totalMem / 1024 / 1024 / 1024).toFixed(2)}GB\n`;
      output += `🔧 CPU核心: ${cpus.length}个 (${cpus[0]?.model || 'Unknown'})\n\n`;
      
      output += `🛡️ 终端工具安全配置\n\n`;
      output += `✅ 允许的命令数量: ${TERMINAL_CONFIG.allowedCommands.length}个\n`;
      output += `❌ 禁止的命令数量: ${TERMINAL_CONFIG.blockedCommands.length}个\n`;
      output += `📁 允许的工作目录: ${TERMINAL_CONFIG.allowedDirectories.join(', ')}\n`;
      output += `⏱️ 命令超时时间: ${TERMINAL_CONFIG.timeout / 1000}秒\n\n`;
      
      output += `🔧 当前工作目录: ${process.cwd()}\n`;
      output += `🌍 环境变量PATH: ${process.env.PATH ? '已配置' : '未配置'}\n`;
      
      return output;
      
    } catch (error: any) {
      console.error('获取系统信息失败:', error);
      return `❌ 获取系统信息失败: ${error?.message || error}`;
    }
  }
};

/**
 * 导出终端工具
 */
export const terminalTools = {
  terminal: terminalTool,
  terminalInfo: terminalInfoTool
};
