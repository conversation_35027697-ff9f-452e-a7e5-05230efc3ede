# 🖥️ 终端工具测试指南

## 🎯 测试目标

验证新增的终端工具功能，包括：
1. **安全命令执行** - 执行白名单内的安全命令
2. **安全机制验证** - 阻止危险命令执行
3. **系统信息查询** - 获取系统和终端信息
4. **工具链集成** - 终端工具与工具链的集成
5. **跨平台兼容** - Windows/Linux/macOS兼容性

## 🛡️ 安全特性

### ✅ 允许的命令类型
- **文件操作**: ls, dir, cat, mkdir, cp, mv
- **系统信息**: pwd, whoami, date, ps, uname
- **开发工具**: node, npm, git, python
- **网络工具**: ping, curl, wget
- **文本处理**: grep, sort, head, tail

### ❌ 禁止的命令类型
- **删除操作**: rm, del, format
- **系统控制**: shutdown, reboot, halt
- **权限修改**: sudo, chmod, chown
- **进程控制**: kill, killall, pkill

### 🔒 安全限制
- 命令执行超时: 30秒
- 工作目录限制: ./workspace, ./temp, ./output, ./logs
- 输出缓冲区限制: 1MB
- 危险参数检测: --force, -r, &&, ||, ;

## 📋 测试场景

### 场景1: 基础命令执行测试
**目标**: 验证基本的安全命令执行功能

#### 测试命令
```bash
# 1. 查看当前目录
#tool:terminal pwd

# 2. 列出文件
#tool:terminal ls -la

# 3. 查看系统信息
#tool:terminal uname -a

# 4. 检查Node版本
#tool:terminal node --version

# 5. 查看当前用户
#tool:terminal whoami
```

### 场景2: 安全机制测试
**目标**: 验证安全限制是否有效

#### 测试命令
```bash
# 1. 尝试危险删除命令（应被阻止）
#tool:terminal rm -rf /

# 2. 尝试系统关机命令（应被阻止）
#tool:terminal shutdown -h now

# 3. 尝试权限修改命令（应被阻止）
#tool:terminal sudo ls

# 4. 尝试进程终止命令（应被阻止）
#tool:terminal kill -9 1

# 5. 尝试未知命令（应被阻止）
#tool:terminal unknowncommand123
```

### 场景3: 系统信息查询测试
**目标**: 验证系统信息获取功能

#### 测试命令
```bash
# 1. 获取终端信息
#tool:terminalInfo

# 2. 查看系统详细信息
#tool:terminal date

# 3. 查看内存使用情况（如果支持）
#tool:terminal free -h

# 4. 查看磁盘使用情况
#tool:terminal df -h
```

### 场景4: 开发环境检查测试
**目标**: 验证开发工具版本检查

#### 测试命令
```bash
# 1. Node.js版本
#tool:terminal node --version

# 2. NPM版本
#tool:terminal npm --version

# 3. Git版本
#tool:terminal git --version

# 4. Python版本（如果安装）
#tool:terminal python --version

# 5. 检查包管理器
#tool:terminal which npm
```

### 场景5: 工具链集成测试
**目标**: 验证终端工具与工具链的集成

#### 测试命令
```bash
# 1. 系统信息工具链
#tool:toolChain 系统信息

# 2. 开发环境检查工具链
#tool:toolChain 检查环境

# 3. 智能任务识别测试
执行一些系统命令

# 4. 智能任务识别测试
查看系统版本信息
```

### 场景6: 文件操作测试
**目标**: 验证安全的文件操作功能

#### 测试命令
```bash
# 1. 创建目录
#tool:terminal mkdir ./workspace/test

# 2. 创建文件
#tool:terminal echo "Hello Terminal" > ./workspace/test/hello.txt

# 3. 查看文件内容
#tool:terminal cat ./workspace/test/hello.txt

# 4. 列出目录内容
#tool:terminal ls ./workspace/test

# 5. 复制文件
#tool:terminal cp ./workspace/test/hello.txt ./workspace/test/hello_copy.txt
```

### 场景7: 网络工具测试
**目标**: 验证网络相关命令

#### 测试命令
```bash
# 1. Ping测试
#tool:terminal ping -c 4 8.8.8.8

# 2. 域名解析（如果支持）
#tool:terminal nslookup google.com

# 3. 检查网络连接（如果支持curl）
#tool:terminal curl -I https://httpbin.org/get
```

## 📊 预期结果

### ✅ 成功指标
- 所有安全命令正常执行
- 危险命令被成功阻止
- 系统信息正确获取
- 工具链集成正常工作
- 错误处理机制完善

### ⚠️ 注意事项
- 某些命令可能在不同操作系统上有差异
- 网络命令可能受防火墙影响
- 部分开发工具可能未安装

## 🚀 快速测试脚本

### 基础功能验证
```bash
# 快速验证终端工具是否正常工作
#tool:terminal echo "Terminal tool is working!"
#tool:terminalInfo
```

### 安全机制验证
```bash
# 快速验证安全机制是否有效
#tool:terminal rm -rf test
#tool:terminal sudo ls
```

### 工具链验证
```bash
# 快速验证工具链集成
#tool:toolChain 系统信息
```

## 📈 测试评估标准

### 功能性评估 (40分)
- [ ] 基础命令执行 (10分)
- [ ] 安全机制有效 (10分)
- [ ] 系统信息获取 (10分)
- [ ] 工具链集成 (10分)

### 安全性评估 (30分)
- [ ] 危险命令阻止 (15分)
- [ ] 工作目录限制 (10分)
- [ ] 超时机制 (5分)

### 用户体验评估 (20分)
- [ ] 错误信息清晰 (10分)
- [ ] 输出格式友好 (10分)

### 兼容性评估 (10分)
- [ ] 跨平台兼容 (5分)
- [ ] 命令可用性 (5分)

## 🎯 测试完成标准

当以下条件全部满足时，认为测试完成：
1. 所有7个测试场景都已执行
2. 功能性评估得分 ≥ 32分 (80%)
3. 安全性评估得分 ≥ 24分 (80%)
4. 用户体验评估得分 ≥ 16分 (80%)
5. 兼容性评估得分 ≥ 8分 (80%)
6. 总体评分 ≥ 80分 (80%)

## 🔧 故障排除

### 常见问题
1. **命令不存在**: 检查命令是否在系统PATH中
2. **权限不足**: 确认在允许的工作目录内操作
3. **超时错误**: 命令执行时间超过30秒限制
4. **输出截断**: 输出内容超过1MB缓冲区限制

### 解决方案
1. 使用 `which 命令名` 检查命令可用性
2. 使用 `#tool:terminalInfo` 查看系统信息
3. 分解长时间运行的命令
4. 使用管道或重定向减少输出

## 🎉 开始测试

准备好了吗？让我们开始测试这个强大的终端工具系统！

### 第一步：基础验证
```bash
#tool:terminal echo "🚀 Terminal tool test started!"
```

### 第二步：系统信息
```bash
#tool:terminalInfo
```

### 第三步：安全测试
```bash
#tool:terminal rm test  # 这应该被阻止
```

让我们开始这个激动人心的测试之旅！🎯
