import { test, expect } from '@playwright/test';

// 端到端测试：主页加载、输入任务、查看历史

test('首页加载与UI交互', async ({ page }) => {
  await page.goto('http://localhost:5173');
  await expect(page).toHaveTitle(/Vite \+ React/);
  // 检查输入框存在
  await expect(page.getByPlaceholder('请输入任务')).toBeVisible();
  // 输入任务并提交
  await page.getByPlaceholder('请输入任务').fill('测试任务1');
  await page.getByRole('button', { name: /发送|提交|Send/ }).click();
  // 检查历史区是否出现新内容
  await expect(page.getByText('测试任务1')).toBeVisible();
});
