/**
 * 多步处理智能体实现，支持长时间运行和自动多步处理任务
 */
import { Agent, AgentInput, AgentOutput, AgentMemory } from '../common/agent';
import { ToolPlugin } from '../common/plugin';
import { AIAgent } from './aiAgent';

interface TaskStep {
  id: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: string;
  error?: string;
  timestamp: number;
}

interface MultiStepConfig {
  maxSteps: number;
  maxDuration: number; // 最大执行时间（毫秒）
  autoStop: boolean; // 是否自动停止
}

export class MultiStepAgent implements Agent {
  id: string;
  name: string;
  memory: AgentMemory;
  tools: ToolPlugin[];
  private aiAgent: AIAgent;
  private config: MultiStepConfig;
  private steps: TaskStep[] = [];
  private isRunning: boolean = false;
  private startTime: number = 0;

  constructor(
    id: string, 
    name: string, 
    memory: AgentMemory, 
    tools: ToolPlugin[] = [], 
    modelConfig: any,
    config: Partial<MultiStepConfig> = {}
  ) {
    this.id = id;
    this.name = name;
    this.memory = memory;
    this.tools = tools;
    
    // 创建内部AI智能体
    this.aiAgent = new AIAgent(id, name, memory, tools, modelConfig);
    
    // 配置默认值
    this.config = {
      maxSteps: config.maxSteps || 10,
      maxDuration: config.maxDuration || 300000, // 5分钟
      autoStop: config.autoStop !== false
    };
  }

  /**
   * 单步执行（兼容原有接口）
   */
  async act(input: AgentInput): Promise<AgentOutput> {
    return await this.aiAgent.act(input);
  }

  /**
   * 多步执行主方法
   */
  async executeMultiStep(input: AgentInput): Promise<{
    steps: TaskStep[];
    finalResult: string;
    status: 'completed' | 'stopped' | 'failed' | 'timeout';
    duration: number;
  }> {
    this.isRunning = true;
    this.startTime = Date.now();
    this.steps = [];

    try {
      // 第一步：任务分析和规划
      const planningResult = await this.planTask(input);
      
      // 执行多步处理
      let currentInput = input;
      let stepCount = 0;
      let finalResult = '';

      while (this.shouldContinue(stepCount)) {
        stepCount++;
        
        const step: TaskStep = {
          id: `step-${stepCount}`,
          description: `执行第${stepCount}步`,
          status: 'running',
          timestamp: Date.now()
        };
        
        this.steps.push(step);

        try {
          // 执行当前步骤
          const stepResult = await this.executeStep(currentInput, stepCount);
          
          step.status = 'completed';
          step.result = stepResult.content;
          finalResult = stepResult.content;

          // 检查是否需要继续
          const shouldContinue = await this.decideContinuation(stepResult.content, stepCount);
          if (!shouldContinue) {
            break;
          }

          // 准备下一步的输入
          currentInput = {
            ...input,
            content: `基于前面的结果继续处理：${stepResult.content}`
          };

        } catch (error) {
          step.status = 'failed';
          step.error = error instanceof Error ? error.message : '未知错误';
          break;
        }
      }

      const duration = Date.now() - this.startTime;
      const status = this.determineStatus(stepCount);

      return {
        steps: this.steps,
        finalResult,
        status,
        duration
      };

    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 任务规划
   */
  private async planTask(input: AgentInput): Promise<string> {
    const planningPrompt = {
      ...input,
      content: `请分析以下任务并制定执行计划：${input.content}\n\n请说明：1. 任务的主要目标 2. 需要的步骤 3. 可能用到的工具`
    };

    const result = await this.aiAgent.act(planningPrompt);
    
    const planningStep: TaskStep = {
      id: 'planning',
      description: '任务规划',
      status: 'completed',
      result: result.content,
      timestamp: Date.now()
    };
    
    this.steps.push(planningStep);
    return result.content;
  }

  /**
   * 执行单个步骤
   */
  private async executeStep(input: AgentInput, stepNumber: number): Promise<AgentOutput> {
    const stepPrompt = {
      ...input,
      content: `${input.content}\n\n这是第${stepNumber}步执行。请根据当前情况决定需要采取的行动。如果需要使用工具，请直接调用。`
    };

    return await this.aiAgent.act(stepPrompt);
  }

  /**
   * 决定是否继续执行
   */
  private async decideContinuation(lastResult: string, stepCount: number): Promise<boolean> {
    if (!this.config.autoStop) {
      return false; // 如果不自动停止，只执行一步
    }

    // 询问AI是否需要继续
    const decisionPrompt: AgentInput = {
      userId: 'system',
      content: `基于以下结果，判断任务是否已经完成，还是需要继续执行下一步？\n\n当前结果：${lastResult}\n\n请回答：CONTINUE（继续）或 STOP（停止），并简要说明原因。`
    };

    const decision = await this.aiAgent.act(decisionPrompt);
    return decision.content.toUpperCase().includes('CONTINUE');
  }

  /**
   * 检查是否应该继续执行
   */
  private shouldContinue(stepCount: number): boolean {
    if (!this.isRunning) return false;
    if (stepCount >= this.config.maxSteps) return false;
    if (Date.now() - this.startTime > this.config.maxDuration) return false;
    return true;
  }

  /**
   * 确定最终状态
   */
  private determineStatus(stepCount: number): 'completed' | 'stopped' | 'failed' | 'timeout' {
    if (Date.now() - this.startTime > this.config.maxDuration) {
      return 'timeout';
    }
    if (stepCount >= this.config.maxSteps) {
      return 'stopped';
    }
    if (this.steps.some(step => step.status === 'failed')) {
      return 'failed';
    }
    return 'completed';
  }

  /**
   * 获取执行状态
   */
  getExecutionStatus(): {
    isRunning: boolean;
    steps: TaskStep[];
    duration: number;
  } {
    return {
      isRunning: this.isRunning,
      steps: this.steps,
      duration: this.isRunning ? Date.now() - this.startTime : 0
    };
  }

  /**
   * 停止执行
   */
  stop(): void {
    this.isRunning = false;
  }
}
