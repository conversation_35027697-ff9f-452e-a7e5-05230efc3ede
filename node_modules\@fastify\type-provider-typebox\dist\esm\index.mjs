import { TypeCompiler } from '@sinclair/typebox/compiler';
import { Value } from '@sinclair/typebox/value';
export * from '@sinclair/typebox';
export const TypeBoxValidatorCompiler = ({ schema, httpPart }) => {
    const typeCheck = TypeCompiler.Compile(schema);
    return (value) => {
        const converted = httpPart === 'body' ? value : Value.Convert(schema, value);
        if (typeCheck.Check(converted)) {
            return { value: converted };
        }
        const errors = [];
        for (const error of typeCheck.Errors(converted)) {
            errors.push({
                message: error.message,
                instancePath: error.path
            });
        }
        return {
            error: errors
        };
    };
};
