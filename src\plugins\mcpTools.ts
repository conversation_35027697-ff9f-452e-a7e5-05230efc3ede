/**
 * MCP工具插件 - 将MCP服务器的工具集成到现有插件系统
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import { MCPClientManager } from '../mcp/mcpClient';

// 全局MCP客户端管理器实例
let mcpManager: MCPClientManager | null = null;

/**
 * 初始化MCP管理器
 */
async function initMCPManager(): Promise<MCPClientManager> {
  if (!mcpManager) {
    mcpManager = new MCPClientManager();
    await mcpManager.startAllServers();
  }
  return mcpManager;
}

/**
 * MCP时间工具
 */
export const mcpTimeTool: ToolPlugin = {
  name: 'mcpTime',
  description: '获取当前时间和时区转换（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      
      // 解析输入，确定要调用的具体工具
      if (input.includes('时区') || input.includes('timezone')) {
        // 时区转换
        const result = await manager.callTool('convert_timezone', {
          datetime: new Date().toISOString(),
          from_timezone: 'UTC',
          to_timezone: 'Asia/Shanghai'
        });
        return `时区转换结果: ${JSON.stringify(result.content)}`;
      } else {
        // 获取当前时间
        const result = await manager.callTool('get_current_time', {});
        return `当前时间: ${JSON.stringify(result.content)}`;
      }
    } catch (error: any) {
      console.error('MCP时间工具调用失败:', error);
      return `时间工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * MCP网络获取工具（替代webSearch）
 */
export const mcpFetchTool: ToolPlugin = {
  name: 'mcpFetch',
  description: '获取网页内容（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      
      // 从输入中提取URL，如果没有URL则返回提示
      const urlMatch = input.match(/https?:\/\/[^\s]+/);
      if (!urlMatch) {
        return '请提供要获取的网页URL，例如：https://example.com';
      }
      
      const url = urlMatch[0];
      const result = await manager.callTool('fetch_url', { url });
      
      return `网页内容获取成功:\n标题: ${result.content?.title || '未知'}\n内容: ${result.content?.text?.substring(0, 1000) || '无内容'}...`;
    } catch (error: any) {
      console.error('MCP网络获取工具调用失败:', error);
      return `网络获取工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * MCP文件系统工具
 */
export const mcpFileSystemTool: ToolPlugin = {
  name: 'mcpFileSystem',
  description: '文件系统操作（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      
      // 解析操作类型
      if (input.includes('读取') || input.includes('read')) {
        // 读取文件
        const pathMatch = input.match(/[^\s]+\.(txt|json|md|js|ts|py)/);
        if (!pathMatch) {
          return '请指定要读取的文件路径，例如：读取 example.txt';
        }
        
        const result = await manager.callTool('read_file', { path: pathMatch[0] });
        return `文件内容:\n${result.content}`;
        
      } else if (input.includes('列表') || input.includes('list')) {
        // 列出目录
        const result = await manager.callTool('list_directory', { path: '.' });
        return `目录内容:\n${JSON.stringify(result.content, null, 2)}`;
        
      } else {
        return '支持的操作：读取文件、列出目录。例如：读取 example.txt 或 列表 ./';
      }
    } catch (error: any) {
      console.error('MCP文件系统工具调用失败:', error);
      return `文件系统工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * MCP记忆工具
 */
export const mcpMemoryTool: ToolPlugin = {
  name: 'mcpMemory',
  description: '持久化记忆系统（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      
      if (input.includes('记住') || input.includes('存储')) {
        // 存储记忆
        const content = input.replace(/记住|存储/, '').trim();
        const result = await manager.callTool('store_memory', {
          key: `memory_${Date.now()}`,
          value: content
        });
        return `记忆已存储: ${content}`;
        
      } else if (input.includes('回忆') || input.includes('检索')) {
        // 检索记忆
        const query = input.replace(/回忆|检索/, '').trim();
        const result = await manager.callTool('search_memory', { query });
        return `相关记忆:\n${JSON.stringify(result.content, null, 2)}`;
        
      } else {
        return '支持的操作：记住内容、回忆内容。例如：记住今天是个好日子 或 回忆好日子';
      }
    } catch (error: any) {
      console.error('MCP记忆工具调用失败:', error);
      return `记忆工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * 获取MCP工具状态
 */
export const mcpStatusTool: ToolPlugin = {
  name: 'mcpStatus',
  description: '查看MCP工具状态',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      const status = manager.getServerStatus();
      const tools = manager.getAllTools();
      
      let result = 'MCP服务器状态:\n';
      for (const [name, info] of Object.entries(status)) {
        result += `- ${name}: ${info.connected ? '✅ 已连接' : '❌ 未连接'} (${info.toolCount}个工具) - ${info.description}\n`;
      }
      
      result += '\n可用工具:\n';
      for (const tool of tools) {
        result += `- ${tool.name} (${tool.server}): ${tool.description}\n`;
      }
      
      return result;
    } catch (error: any) {
      console.error('MCP状态查询失败:', error);
      return `MCP状态查询失败: ${error?.message || error}`;
    }
  }
};

/**
 * 导出所有MCP工具
 */
export const mcpTools = {
  mcpTime: mcpTimeTool,
  mcpFetch: mcpFetchTool,
  mcpFileSystem: mcpFileSystemTool,
  mcpMemory: mcpMemoryTool,
  mcpStatus: mcpStatusTool
};

/**
 * 清理MCP资源
 */
export async function cleanupMCP(): Promise<void> {
  if (mcpManager) {
    await mcpManager.stopAllServers();
    mcpManager = null;
  }
}
