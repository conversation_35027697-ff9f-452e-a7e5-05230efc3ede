/**
 * MCP工具插件 - 将MCP服务器的工具集成到现有插件系统
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import { MCPClientManager } from '../mcp/mcpClient';

// 全局MCP客户端管理器实例
let mcpManager: MCPClientManager | null = null;

/**
 * 初始化MCP管理器
 */
async function initMCPManager(): Promise<MCPClientManager> {
  if (!mcpManager) {
    mcpManager = new MCPClientManager();
    await mcpManager.startAllServers();
  }
  return mcpManager;
}

/**
 * MCP时间工具
 */
export const mcpTimeTool: ToolPlugin = {
  name: 'mcpTime',
  description: '获取当前时间和时区转换（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      
      // 解析输入，确定要调用的具体工具
      if (input.includes('时区') || input.includes('timezone')) {
        // 时区转换
        const result = await manager.callTool('convert_timezone', {
          datetime: new Date().toISOString(),
          from_timezone: 'UTC',
          to_timezone: 'Asia/Shanghai'
        });
        return `时区转换结果: ${JSON.stringify(result.content)}`;
      } else {
        // 获取当前时间 - 默认使用Asia/Shanghai时区
        const result = await manager.callTool('get_current_time', { timezone: 'Asia/Shanghai' });
        return `当前时间: ${JSON.stringify(result.content)}`;
      }
    } catch (error: any) {
      console.error('MCP时间工具调用失败:', error);
      return `时间工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * MCP网络获取工具（替代webSearch）
 */
export const mcpFetchTool: ToolPlugin = {
  name: 'mcpFetch',
  description: '获取网页内容（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();

      // 从输入中提取URL，如果没有URL则返回提示
      const urlMatch = input.match(/https?:\/\/[^\s]+/);
      if (!urlMatch) {
        return '请提供要获取的网页URL，例如：https://example.com';
      }

      const url = urlMatch[0];
      console.log(`尝试获取URL: ${url}`);

      const result = await manager.callTool('fetch', { url });

      // 检查结果格式
      if (result && result.content) {
        // 如果是数组格式的内容
        if (Array.isArray(result.content)) {
          const textContent = result.content
            .filter((item: any) => item.type === 'text')
            .map((item: any) => item.text)
            .join('\n');

          if (textContent.includes('Failed to fetch') || textContent.includes('connection issue')) {
            return `网络获取失败: ${textContent}`;
          }

          return `网页内容获取成功:\n${textContent}`;
        } else {
          return `网页内容获取成功:\n${JSON.stringify(result.content, null, 2)}`;
        }
      } else {
        return `网页内容获取成功:\n${JSON.stringify(result, null, 2)}`;
      }
    } catch (error: any) {
      console.error('MCP网络获取工具调用失败:', error);
      return `网络获取工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * MCP文件系统工具
 */
export const mcpFileSystemTool: ToolPlugin = {
  name: 'mcpFileSystem',
  description: '文件系统操作（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      
      // 解析操作类型
      if (input.includes('读取') || input.includes('read')) {
        // 读取文件
        const pathMatch = input.match(/[^\s]+\.(txt|json|md|js|ts|py)/);
        if (!pathMatch) {
          return '请指定要读取的文件路径，例如：读取 example.txt';
        }
        
        const result = await manager.callTool('read_file', { path: pathMatch[0] });
        return `文件内容:\n${result.content}`;
        
      } else if (input.includes('列表') || input.includes('list') || input.includes('列出')) {
        // 列出目录 - 使用当前工作目录，MCP服务器会自动限制在允许的目录内
        const result = await manager.callTool('list_directory', { path: '.' });
        return `目录内容:\n${JSON.stringify(result.content, null, 2)}`;

      } else if (input.includes('创建文件') || input.includes('写入')) {
        // 创建文件
        const pathMatch = input.match(/[^\s]+\.(txt|json|md|js|ts|py)/);
        if (!pathMatch) {
          return '请指定要创建的文件路径，例如：创建文件 test.txt';
        }

        const content = input.includes('内容') ? input.split('内容')[1]?.trim() || 'Hello World!' : 'Hello World!';
        const result = await manager.callTool('write_file', {
          path: pathMatch[0],
          content
        });
        return `文件创建成功:\n${JSON.stringify(result.content, null, 2)}`;

      } else if (input.includes('允许目录') || input.includes('allowed')) {
        // 列出允许访问的目录
        const result = await manager.callTool('list_allowed_directories', {});
        return `允许访问的目录:\n${JSON.stringify(result.content, null, 2)}`;

      } else {
        return '支持的操作：读取文件、列出目录、创建文件、查看允许目录。例如：读取 example.txt 或 列出workspace目录 或 创建文件 test.txt 或 允许目录';
      }
    } catch (error: any) {
      console.error('MCP文件系统工具调用失败:', error);
      return `文件系统工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * MCP记忆工具
 */
export const mcpMemoryTool: ToolPlugin = {
  name: 'mcpMemory',
  description: '持久化记忆系统（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      
      if (input.includes('创建实体') || (input.includes('创建') && input.includes('实体'))) {
        // 创建实体
        const nameMatch = input.match(/名称为["""]([^"""]+)["""]/);
        const descMatch = input.match(/描述为["""]([^"""]+)["""]/);

        if (nameMatch && descMatch) {
          const result = await manager.callTool('create_entities', {
            entities: [{
              name: nameMatch[1],
              entityType: 'concept',
              observations: [descMatch[1]]
            }]
          });
          return `实体创建成功:\n${JSON.stringify(result.content, null, 2)}`;
        } else {
          return '请提供实体名称和描述，格式：创建实体：名称为"实体名"，描述为"实体描述"';
        }

      } else if (input.includes('搜索') || input.includes('查找')) {
        // 搜索节点
        const query = input.replace(/搜索|查找/, '').trim();
        const result = await manager.callTool('search_nodes', { query });
        return `搜索结果:\n${JSON.stringify(result.content, null, 2)}`;

      } else if (input.includes('读取图谱') || input.includes('查看图谱')) {
        // 读取整个知识图谱
        const result = await manager.callTool('read_graph', {});
        return `知识图谱内容:\n${JSON.stringify(result.content, null, 2)}`;

      } else {
        return '支持的操作：创建实体、搜索节点、读取图谱。例如：创建实体：名称为"测试"，描述为"这是测试" 或 搜索测试 或 读取图谱';
      }
    } catch (error: any) {
      console.error('MCP记忆工具调用失败:', error);
      return `记忆工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * MCP Puppeteer工具
 */
export const mcpPuppeteerTool: ToolPlugin = {
  name: 'mcpPuppeteer',
  description: '浏览器自动化工具（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();

      if (input.includes('截图') || input.includes('screenshot')) {
        // 截图功能
        const urlMatch = input.match(/https?:\/\/[^\s]+/);
        if (!urlMatch) {
          return '请提供要截图的网页URL，例如：截图 https://example.com';
        }

        const url = urlMatch[0];
        const result = await manager.callTool('puppeteer_screenshot', { url });
        return `截图完成:\n${JSON.stringify(result.content, null, 2)}`;

      } else if (input.includes('导航') || input.includes('navigate')) {
        // 导航功能
        const urlMatch = input.match(/https?:\/\/[^\s]+/);
        if (!urlMatch) {
          return '请提供要导航的网页URL，例如：导航 https://example.com';
        }

        const url = urlMatch[0];
        const result = await manager.callTool('puppeteer_navigate', { url });
        return `导航完成:\n${JSON.stringify(result.content, null, 2)}`;

      } else if (input.includes('点击') || input.includes('click')) {
        // 点击功能
        const selectorMatch = input.match(/选择器["""]([^"""]+)["""]/);
        if (!selectorMatch) {
          return '请提供CSS选择器，例如：点击 选择器"button.submit"';
        }

        const selector = selectorMatch[1];
        const result = await manager.callTool('puppeteer_click', { selector });
        return `点击完成:\n${JSON.stringify(result.content, null, 2)}`;

      } else {
        return '支持的操作：截图、导航、点击。例如：截图 https://example.com 或 导航 https://example.com 或 点击 选择器"button"';
      }
    } catch (error: any) {
      console.error('MCP Puppeteer工具调用失败:', error);
      return `Puppeteer工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * MCP Everything工具
 */
export const mcpEverythingTool: ToolPlugin = {
  name: 'mcpEverything',
  description: '测试和演示工具（通过MCP）',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();

      if (input.includes('回显') || input.includes('echo')) {
        // 回显功能
        const message = input.replace(/回显|echo/gi, '').trim();
        const result = await manager.callTool('echo', { message: message || 'Hello World!' });
        return `回显结果:\n${JSON.stringify(result.content, null, 2)}`;

      } else if (input.includes('相加') || input.includes('add')) {
        // 数字相加
        const numbers = input.match(/\d+/g);
        if (!numbers || numbers.length < 2) {
          return '请提供两个数字，例如：相加 5 和 3';
        }

        const a = parseInt(numbers[0]);
        const b = parseInt(numbers[1]);
        const result = await manager.callTool('add', { a, b });
        return `计算结果:\n${JSON.stringify(result.content, null, 2)}`;

      } else if (input.includes('环境变量') || input.includes('env')) {
        // 打印环境变量
        const result = await manager.callTool('printEnv', {});
        return `环境变量:\n${JSON.stringify(result.content, null, 2)}`;

      } else if (input.includes('长时间') || input.includes('long')) {
        // 长时间运行操作
        const duration = input.match(/\d+/)?.[0] || '5';
        const result = await manager.callTool('longRunningOperation', { duration: parseInt(duration) });
        return `长时间操作完成:\n${JSON.stringify(result.content, null, 2)}`;

      } else {
        return '支持的操作：回显、相加、环境变量、长时间操作。例如：回显 Hello 或 相加 5 和 3 或 环境变量 或 长时间操作 10秒';
      }
    } catch (error: any) {
      console.error('MCP Everything工具调用失败:', error);
      return `Everything工具暂时不可用: ${error?.message || error}`;
    }
  }
};

/**
 * 获取MCP工具状态
 */
export const mcpStatusTool: ToolPlugin = {
  name: 'mcpStatus',
  description: '查看MCP工具状态',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const manager = await initMCPManager();
      const status = manager.getServerStatus();
      const tools = manager.getAllTools();

      let result = 'MCP服务器状态:\n';
      for (const [name, info] of Object.entries(status)) {
        result += `- ${name}: ${info.connected ? '✅ 已连接' : '❌ 未连接'} (${info.toolCount}个工具) - ${info.description}\n`;
      }

      result += '\n可用工具:\n';
      for (const tool of tools) {
        result += `- ${tool.name} (${tool.server}): ${tool.description}\n`;
      }

      return result;
    } catch (error: any) {
      console.error('MCP状态查询失败:', error);
      return `MCP状态查询失败: ${error?.message || error}`;
    }
  }
};

/**
 * 导出所有MCP工具
 */
export const mcpTools = {
  mcpTime: mcpTimeTool,
  mcpFetch: mcpFetchTool,
  mcpFileSystem: mcpFileSystemTool,
  mcpMemory: mcpMemoryTool,
  mcpPuppeteer: mcpPuppeteerTool,
  mcpEverything: mcpEverythingTool,
  mcpStatus: mcpStatusTool
};

/**
 * 清理MCP资源
 */
export async function cleanupMCP(): Promise<void> {
  if (mcpManager) {
    await mcpManager.stopAllServers();
    mcpManager = null;
  }
}
