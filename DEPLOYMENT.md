# 部署指南

## 开发环境部署

### 前置要求
- Node.js 18+ 
- npm 或 yarn
- Git

### 快速启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd xin

# 2. 安装后端依赖
npm install

# 3. 安装前端依赖
cd web
npm install
cd ..

# 4. 启动开发环境（同时启动前后端）
npm run dev
```

### 分别启动
```bash
# 启动后端API服务器
npm run start:server

# 启动前端开发服务器（新终端）
cd web && npm run dev
```

### 访问地址
- 前端界面：http://localhost:5173
- 后端API：http://localhost:3001

## 生产环境部署

### 构建项目
```bash
# 构建前后端
npm run build
```

### 使用 PM2 部署
```bash
# 安装 PM2
npm install -g pm2

# 启动后端服务
pm2 start "npx ts-node src/api/server.ts" --name "xin-api"

# 构建并部署前端
cd web
npm run build
# 将 dist 目录部署到 Nginx 或其他静态文件服务器
```

### Docker 部署
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
COPY web/package*.json ./web/

# 安装依赖
RUN npm install
RUN cd web && npm install

# 复制源代码
COPY . .

# 构建前端
RUN cd web && npm run build

# 暴露端口
EXPOSE 3001 5173

# 启动命令
CMD ["npm", "run", "dev"]
```

### 环境变量配置
```bash
# .env
NODE_ENV=production
API_PORT=3001
WEB_PORT=5173
```

## 配置说明

### 系统配置 (config/system.json)
```json
{
  "model": "llama3.2:3b",
  "memory": {
    "type": "inmemory"
  },
  "plugins": ["getTime", "webSearch", "math", "weather"]
}
```

### 插件配置
- 在 `src/plugins/` 目录下添加新插件
- 在 `src/common/factory.ts` 中注册插件
- 在配置文件中启用插件

## 监控和日志

### 日志配置
- 后端日志：控制台输出
- 前端日志：浏览器控制台
- 生产环境建议使用 Winston 或其他日志库

### 健康检查
```bash
# 检查后端API
curl http://localhost:3001/api/agent/plugins

# 检查前端
curl http://localhost:5173
```

## 故障排除

### 常见问题
1. **端口冲突**：修改 package.json 中的端口配置
2. **依赖安装失败**：清除 node_modules 重新安装
3. **API 连接失败**：检查后端服务是否启动
4. **插件错误**：查看控制台日志

### 调试模式
```bash
# 启用详细日志
DEBUG=* npm run start:server
```
