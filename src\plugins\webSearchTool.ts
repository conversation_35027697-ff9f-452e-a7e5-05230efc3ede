/**
 * 网络搜索插件
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import axios from 'axios';

export const webSearchTool: ToolPlugin = {
  name: 'webSearch',
  description: '网络搜索功能，可以搜索互联网上的信息',
  async run(input: string, context: ToolContext): Promise<string> {
    try {
      // 从输入中提取搜索关键词
      const query = input || '当前新闻';
      
      // 使用DuckDuckGo的即时答案API（免费且无需API key）
      const response = await axios.get(`https://api.duckduckgo.com/`, {
        params: {
          q: query,
          format: 'json',
          no_html: '1',
          skip_disambig: '1'
        },
        timeout: 10000
      });

      const data = response.data as any;

      // 构建搜索结果
      let result = `搜索关键词: ${query}\n\n`;

      // 即时答案
      if (data.Answer) {
        result += `即时答案: ${data.Answer}\n\n`;
      }

      // 摘要信息
      if (data.Abstract) {
        result += `摘要: ${data.Abstract}\n\n`;
      }

      // 相关主题
      if (data.RelatedTopics && data.RelatedTopics.length > 0) {
        result += `相关信息:\n`;
        data.RelatedTopics.slice(0, 3).forEach((topic: any, index: number) => {
          if (topic.Text) {
            result += `${index + 1}. ${topic.Text}\n`;
          }
        });
      }

      // 如果没有找到有用信息，返回默认消息
      if (!data.Answer && !data.Abstract && (!data.RelatedTopics || data.RelatedTopics.length === 0)) {
        result += `未找到关于 "${query}" 的详细信息，建议尝试其他关键词。`;
      }
      
      return result;
      
    } catch (error) {
      console.error('Web search error:', error);
      return `搜索失败: ${error instanceof Error ? error.message : '未知错误'}`;
    }
  },
};
