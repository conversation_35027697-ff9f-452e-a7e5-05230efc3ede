/**
 * 网络搜索插件
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

// 获取搜索API密钥
function getSearchApiKey(): string | null {
  try {
    const configPath = path.join(process.cwd(), 'config', 'system.json');
    const configData = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configData);
    return config.apiKeys?.google || null;
  } catch {
    return null;
  }
}

export const webSearchTool: ToolPlugin = {
  name: 'webSearch',
  description: '网络搜索功能，可以搜索互联网上的信息',
  async run(input: string, context: ToolContext): Promise<string> {
    try {
      // 从输入中提取搜索关键词
      const query = input || '当前新闻';
      const apiKey = getSearchApiKey();

      // 如果有Google API密钥，使用真实搜索
      if (apiKey) {
        return await getRealSearch(query, apiKey);
      } else {
        // 否则使用DuckDuckGo的即时答案API
        return await getDuckDuckGoSearch(query);
      }
    } catch (error) {
      console.error('Web search error:', error);
      return `搜索失败: ${error instanceof Error ? error.message : '未知错误'}`;
    }
  },
};

// 使用Google Custom Search API
async function getRealSearch(query: string, apiKey: string): Promise<string> {
  // 这里需要Google Custom Search Engine ID
  const searchEngineId = 'your-search-engine-id'; // 需要配置

  const response = await axios.get('https://www.googleapis.com/customsearch/v1', {
    params: {
      key: apiKey,
      cx: searchEngineId,
      q: query,
      num: 5
    },
    timeout: 10000
  });

  const data = response.data as any;
  let result = `搜索关键词: ${query} (Google Search)\n\n`;

  if (data.items && data.items.length > 0) {
    result += `找到 ${data.items.length} 条结果:\n\n`;
    data.items.forEach((item: any, index: number) => {
      result += `${index + 1}. ${item.title}\n`;
      result += `   ${item.snippet}\n`;
      result += `   链接: ${item.link}\n\n`;
    });
  } else {
    result += '未找到相关结果。';
  }

  return result;
}

// 使用DuckDuckGo即时答案API
async function getDuckDuckGoSearch(query: string): Promise<string> {
  try {
    const response = await axios.get(`https://api.duckduckgo.com/`, {
      params: {
        q: query,
        format: 'json',
        no_html: '1',
        skip_disambig: '1'
      },
      timeout: 8000
    });

    const data = response.data as any;
    let result = `搜索关键词: ${query} (DuckDuckGo)\n\n`;

    // 即时答案
    if (data.Answer) {
      result += `即时答案: ${data.Answer}\n\n`;
    }

    // 摘要信息
    if (data.Abstract) {
      result += `摘要: ${data.Abstract}\n\n`;
    }

    // 相关主题
    if (data.RelatedTopics && data.RelatedTopics.length > 0) {
      result += `相关信息:\n`;
      data.RelatedTopics.slice(0, 3).forEach((topic: any, index: number) => {
        if (topic.Text) {
          result += `${index + 1}. ${topic.Text}\n`;
        }
      });
      result += '\n';
    }

    // 如果没有找到有用信息，返回默认消息
    if (!data.Answer && !data.Abstract && (!data.RelatedTopics || data.RelatedTopics.length === 0)) {
      result += `未找到关于 "${query}" 的详细信息。\n\n`;
      result += `提示: 您可以在设置中配置Google Search API密钥以获得更好的搜索结果。`;
    }

    return result;
  } catch (error) {
    // 如果DuckDuckGo API失败，返回模拟结果
    return getSimulatedSearch(query);
  }
}

// 模拟搜索结果（作为后备方案）
function getSimulatedSearch(query: string): Promise<string> {
  const mockResults = [
    {
      title: `关于"${query}"的相关信息`,
      snippet: "这是一个模拟的搜索结果。在实际使用中，建议配置真实的搜索API密钥。",
      url: "https://example.com/search-result-1"
    },
    {
      title: `${query} - 详细介绍`,
      snippet: "更多相关内容和详细信息可以通过配置Google Search API获得。",
      url: "https://example.com/search-result-2"
    }
  ];

  let result = `搜索关键词: ${query} (模拟结果)\n\n`;
  result += `找到 ${mockResults.length} 条模拟结果:\n\n`;

  mockResults.forEach((item, index) => {
    result += `${index + 1}. ${item.title}\n`;
    result += `   ${item.snippet}\n`;
    result += `   链接: ${item.url}\n\n`;
  });

  result += `💡 提示: 在设置中配置Google Search API密钥可获得真实搜索结果。`;

  return Promise.resolve(result);
}
