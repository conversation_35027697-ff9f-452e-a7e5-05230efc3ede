{"name": "xin", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/axios": "^0.9.36", "@types/jest": "^30.0.0", "@types/node": "^24.0.13", "jest": "^29.7.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/type-provider-typebox": "^5.2.0", "@playwright/test": "^1.54.1", "axios": "^1.10.0", "fastify": "^5.4.0"}}