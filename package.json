{"name": "xin", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "start": "npx ts-node src/index.ts", "start:server": "npx ts-node src/api/server.ts", "dev": "concurrently \"npm run start:server\" \"cd web && npm run dev\"", "build": "tsc && cd web && npm run build", "lint": "eslint src/**/*.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/axios": "^0.9.36", "@types/jest": "^30.0.0", "@types/node": "^24.0.13", "concurrently": "^9.2.0", "jest": "^29.7.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/type-provider-typebox": "^5.2.0", "@playwright/test": "^1.54.1", "axios": "^1.10.0", "fastify": "^5.4.0", "lucide-react": "^0.525.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0"}}