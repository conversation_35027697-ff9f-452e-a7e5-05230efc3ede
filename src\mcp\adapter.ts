/**
 * MCP工具适配器 - 将现有插件转换为MCP格式
 */
import { ToolPlugin } from '../common/plugin';
import { MCPTool, MCPToolCall, MCPToolResult } from './types';

export class MCPToolAdapter {
  /**
   * 将ToolPlugin转换为MCPTool
   */
  static pluginToMCPTool(plugin: ToolPlugin): MCPTool {
    return {
      name: plugin.name,
      description: plugin.description,
      inputSchema: {
        type: 'object',
        properties: {
          input: {
            type: 'string',
            description: '工具输入参数'
          }
        },
        required: ['input']
      }
    };
  }

  /**
   * 执行MCP工具调用
   */
  static async executeMCPTool(
    plugin: ToolPlugin, 
    toolCall: MCPToolCall, 
    context: { userId: string; agentId: string; memory: any }
  ): Promise<MCPToolResult> {
    try {
      const input = toolCall.arguments.input || '';
      const result = await plugin.run(input, context);
      
      return {
        content: [{
          type: 'text',
          text: result
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: 'text',
          text: `工具执行错误: ${error instanceof Error ? error.message : '未知错误'}`
        }],
        isError: true
      };
    }
  }
}

/**
 * MCP服务器模拟器 - 将现有插件包装为MCP服务
 */
export class MCPServerSimulator {
  private plugins: ToolPlugin[];

  constructor(plugins: ToolPlugin[]) {
    this.plugins = plugins;
  }

  /**
   * 获取工具列表
   */
  listTools(): MCPTool[] {
    return this.plugins.map(plugin => MCPToolAdapter.pluginToMCPTool(plugin));
  }

  /**
   * 调用工具
   */
  async callTool(toolCall: MCPToolCall, context: { userId: string; agentId: string; memory: any }): Promise<MCPToolResult> {
    const plugin = this.plugins.find(p => p.name === toolCall.name);
    if (!plugin) {
      return {
        content: [{
          type: 'text',
          text: `工具 ${toolCall.name} 未找到`
        }],
        isError: true
      };
    }

    return MCPToolAdapter.executeMCPTool(plugin, toolCall, context);
  }

  /**
   * 处理MCP请求
   */
  async handleRequest(method: string, params: any, context: { userId: string; agentId: string; memory: any }): Promise<any> {
    switch (method) {
      case 'tools/list':
        return {
          tools: this.listTools()
        };
      
      case 'tools/call':
        const toolCall: MCPToolCall = {
          name: params.name,
          arguments: params.arguments || {}
        };
        return await this.callTool(toolCall, context);
      
      case 'initialize':
        return {
          protocolVersion: '2024-11-05',
          capabilities: {
            tools: {
              listChanged: false
            }
          },
          serverInfo: {
            name: 'web-agent-mcp-server',
            version: '1.0.0'
          }
        };
      
      default:
        throw new Error(`Unsupported MCP method: ${method}`);
    }
  }
}
