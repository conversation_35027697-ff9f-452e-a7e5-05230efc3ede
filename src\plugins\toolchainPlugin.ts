/**
 * 工具链插件 - 将工具链管理器集成到插件系统
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import { toolChainManager } from '../toolchain/toolchainManager';
import { MCPClientManager } from '../mcp/mcpClient';

// 全局MCP客户端管理器实例
let mcpManager: MCPClientManager | null = null;

/**
 * 初始化工具链管理器
 */
async function initToolChainManager(): Promise<void> {
  if (!mcpManager) {
    mcpManager = new MCPClientManager();
    await mcpManager.startAllServers();
    toolChainManager.setMCPManager(mcpManager);
  }
}

/**
 * 工具链执行插件
 */
export const toolChainExecutor: ToolPlugin = {
  name: 'toolChain',
  description: '智能工具链执行器，自动组合多个工具完成复杂任务',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      await initToolChainManager();

      // 检测是否匹配工具链
      const detectedChain = toolChainManager.detectToolChain(input);
      
      if (!detectedChain) {
        return '未检测到匹配的工具链。可用的工具链触发词：\n' +
               '- 分析网页/网页分析\n' +
               '- 处理文件/文件操作\n' +
               '- 记录时间/时间记录\n' +
               '- 网页截图/截图网页';
      }

      // 执行工具链
      const result = await toolChainManager.executeToolChain(detectedChain, input, context);
      return result;

    } catch (error: any) {
      console.error('工具链执行失败:', error);
      return `工具链执行失败: ${error?.message || error}`;
    }
  }
};

/**
 * 工具链管理插件
 */
export const toolChainManager_plugin: ToolPlugin = {
  name: 'toolChainManager',
  description: '工具链管理器，查看和管理所有可用的工具链',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      if (input.includes('列表') || input.includes('list') || input.includes('查看')) {
        // 列出所有工具链
        const chains = toolChainManager.getAllToolChains();
        let result = '可用工具链列表:\n\n';
        
        chains.forEach((chain, index) => {
          result += `${index + 1}. **${chain.name}** (${chain.id})\n`;
          result += `   描述: ${chain.description}\n`;
          result += `   类别: ${chain.category}\n`;
          result += `   触发词: ${chain.triggers.join(', ')}\n`;
          result += `   步骤数: ${chain.steps.length}\n\n`;
        });

        return result;

      } else if (input.includes('统计') || input.includes('stats')) {
        // 显示统计信息
        const stats = toolChainManager.getStatistics();
        let result = '工具链统计信息:\n\n';
        result += `总工具链数: ${stats.totalChains}\n`;
        result += `总步骤数: ${stats.totalSteps}\n`;
        result += `平均步骤数: ${(stats.totalSteps / stats.totalChains).toFixed(1)}\n\n`;
        
        result += '按类别分布:\n';
        Object.entries(stats.categoryCounts).forEach(([category, count]) => {
          result += `- ${category}: ${count}个\n`;
        });

        return result;

      } else if (input.includes('详情') || input.includes('detail')) {
        // 显示特定工具链详情
        const chainId = input.match(/详情\s+(\S+)/)?.[1] || input.match(/detail\s+(\S+)/)?.[1];
        if (!chainId) {
          return '请指定工具链ID，例如：详情 web-analysis';
        }

        const chains = toolChainManager.getAllToolChains();
        const chain = chains.find(c => c.id === chainId);
        
        if (!chain) {
          return `未找到工具链: ${chainId}`;
        }

        let result = `工具链详情: ${chain.name}\n\n`;
        result += `ID: ${chain.id}\n`;
        result += `描述: ${chain.description}\n`;
        result += `类别: ${chain.category}\n`;
        result += `触发词: ${chain.triggers.join(', ')}\n\n`;
        
        result += '执行步骤:\n';
        chain.steps.forEach((step, index) => {
          result += `${index + 1}. ${step.toolName}\n`;
          result += `   输入: ${step.input}\n`;
          if (step.dependencies) {
            result += `   依赖: ${step.dependencies.join(', ')}\n`;
          }
          if (step.condition) {
            result += `   条件: 有条件执行\n`;
          }
          result += '\n';
        });

        return result;

      } else {
        return '工具链管理器命令:\n' +
               '- 列表/查看: 显示所有工具链\n' +
               '- 统计: 显示统计信息\n' +
               '- 详情 <工具链ID>: 显示特定工具链详情';
      }

    } catch (error: any) {
      console.error('工具链管理器调用失败:', error);
      return `工具链管理器调用失败: ${error?.message || error}`;
    }
  }
};

/**
 * 智能任务分解插件
 */
export const taskDecomposer: ToolPlugin = {
  name: 'taskDecomposer',
  description: '智能任务分解器，将复杂任务分解为可执行的工具链',

  async run(input: string, context: ToolContext): Promise<string> {
    try {
      // 简单的任务分解逻辑
      let result = '任务分解分析:\n\n';
      result += `原始任务: ${input}\n\n`;

      // 检测任务类型
      const taskTypes: string[] = [];
      
      if (input.includes('网页') || input.includes('网站') || input.includes('URL') || input.includes('http')) {
        taskTypes.push('网页处理');
      }
      
      if (input.includes('文件') || input.includes('保存') || input.includes('读取')) {
        taskTypes.push('文件操作');
      }
      
      if (input.includes('时间') || input.includes('记录') || input.includes('日志')) {
        taskTypes.push('时间记录');
      }
      
      if (input.includes('截图') || input.includes('图片')) {
        taskTypes.push('截图操作');
      }

      if (taskTypes.length === 0) {
        result += '未检测到明确的任务类型。\n';
        result += '建议使用更具体的描述，如：\n';
        result += '- 包含网页URL的任务\n';
        result += '- 包含文件操作的任务\n';
        result += '- 包含时间记录的任务\n';
        return result;
      }

      result += `检测到的任务类型: ${taskTypes.join(', ')}\n\n`;

      // 推荐工具链
      const chains = toolChainManager.getAllToolChains();
      const recommendedChains = chains.filter(chain => {
        return taskTypes.some(type => {
          if (type === '网页处理' && chain.category === 'web') return true;
          if (type === '文件操作' && chain.category === 'file') return true;
          if (type === '时间记录' && chain.category === 'time') return true;
          return false;
        });
      });

      if (recommendedChains.length > 0) {
        result += '推荐的工具链:\n';
        recommendedChains.forEach(chain => {
          result += `- ${chain.name}: ${chain.description}\n`;
          result += `  触发词: ${chain.triggers.join(', ')}\n`;
        });
      } else {
        result += '未找到匹配的工具链，可能需要创建新的工具链。\n';
      }

      return result;

    } catch (error: any) {
      console.error('任务分解失败:', error);
      return `任务分解失败: ${error?.message || error}`;
    }
  }
};

/**
 * 导出所有工具链相关工具
 */
export const toolChainTools = {
  toolChain: toolChainExecutor,
  toolChainManager: toolChainManager_plugin,
  taskDecomposer: taskDecomposer
};
