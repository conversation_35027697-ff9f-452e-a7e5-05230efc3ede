/**
 * 工具链管理器 - 实现工具的自动组合和智能调度
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import { MCPClientManager } from '../mcp/mcpClient';

export interface ToolChainStep {
  toolName: string;
  input: string;
  dependencies?: string[]; // 依赖的前置步骤
  condition?: (context: ToolChainContext) => boolean; // 执行条件
  transform?: (input: string, context: ToolChainContext) => string; // 输入转换
}

export interface ToolChainContext {
  userId: string;
  agentId: string;
  stepResults: Map<string, any>; // 存储每个步骤的结果
  globalVariables: Map<string, any>; // 全局变量
  metadata: Record<string, any>; // 元数据
}

export interface ToolChain {
  id: string;
  name: string;
  description: string;
  steps: ToolChainStep[];
  triggers: string[]; // 触发关键词
  category: string;
}

export class ToolChainManager {
  private toolChains: Map<string, ToolChain> = new Map();
  private mcpManager: MCPClientManager | null = null;
  private tools: Map<string, ToolPlugin> = new Map();

  constructor() {
    this.initializeBuiltinChains();
  }

  /**
   * 设置MCP管理器
   */
  setMCPManager(mcpManager: MCPClientManager): void {
    this.mcpManager = mcpManager;
  }

  /**
   * 注册工具
   */
  registerTool(name: string, tool: ToolPlugin): void {
    this.tools.set(name, tool);
  }

  /**
   * 初始化内置工具链
   */
  private initializeBuiltinChains(): void {
    // 1. 网页分析工具链
    this.registerToolChain({
      id: 'web-analysis',
      name: '网页分析工具链',
      description: '获取网页内容并进行分析总结',
      triggers: ['分析网页', '网页分析', '网站分析'],
      category: 'web',
      steps: [
        {
          toolName: 'mcpFetch',
          input: '${url}',
          transform: (input, context) => {
            const url = context.globalVariables.get('url') || input;
            return url;
          }
        },
        {
          toolName: 'mcpMemory',
          input: '创建实体：名称为"网页分析-${timestamp}"，描述为"${fetch_result}"',
          dependencies: ['mcpFetch'],
          transform: (input, context) => {
            const fetchResult = context.stepResults.get('mcpFetch');
            const timestamp = new Date().toISOString();
            return input.replace('${timestamp}', timestamp).replace('${fetch_result}', JSON.stringify(fetchResult));
          }
        }
      ]
    });

    // 2. 文件处理工具链
    this.registerToolChain({
      id: 'file-processing',
      name: '文件处理工具链',
      description: '创建、写入、读取文件的完整流程',
      triggers: ['处理文件', '文件操作', '文件处理'],
      category: 'file',
      steps: [
        {
          toolName: 'mcpFileSystem',
          input: '创建文件 ${filename}',
          transform: (input, context) => {
            const filename = context.globalVariables.get('filename') || 'output.txt';
            return input.replace('${filename}', filename);
          }
        },
        {
          toolName: 'mcpFileSystem',
          input: '读取 ${filename}',
          dependencies: ['mcpFileSystem'],
          transform: (input, context) => {
            const filename = context.globalVariables.get('filename') || 'output.txt';
            return input.replace('${filename}', filename);
          }
        },
        {
          toolName: 'mcpMemory',
          input: '创建实体：名称为"文件操作记录"，描述为"处理了文件${filename}"',
          dependencies: ['mcpFileSystem'],
          transform: (input, context) => {
            const filename = context.globalVariables.get('filename') || 'output.txt';
            return input.replace('${filename}', filename);
          }
        }
      ]
    });

    // 3. 时间和记录工具链
    this.registerToolChain({
      id: 'time-logging',
      name: '时间记录工具链',
      description: '获取当前时间并记录到知识图谱',
      triggers: ['记录时间', '时间记录', '当前时间记录'],
      category: 'time',
      steps: [
        {
          toolName: 'mcpTime',
          input: '获取当前时间'
        },
        {
          toolName: 'mcpMemory',
          input: '创建实体：名称为"时间记录-${timestamp}"，描述为"记录时间：${time_result}"',
          dependencies: ['mcpTime'],
          transform: (input, context) => {
            const timeResult = context.stepResults.get('mcpTime');
            const timestamp = new Date().toISOString();
            return input.replace('${timestamp}', timestamp).replace('${time_result}', JSON.stringify(timeResult));
          }
        }
      ]
    });

    // 4. 网页截图工具链
    this.registerToolChain({
      id: 'web-screenshot',
      name: '网页截图工具链',
      description: '访问网页并截图保存',
      triggers: ['网页截图', '截图网页', '网站截图'],
      category: 'web',
      steps: [
        {
          toolName: 'mcpPuppeteer',
          input: '导航 ${url}',
          transform: (input, context) => {
            const url = context.globalVariables.get('url') || input;
            return input.replace('${url}', url);
          }
        },
        {
          toolName: 'mcpPuppeteer',
          input: '截图 ${url}',
          dependencies: ['mcpPuppeteer'],
          transform: (input, context) => {
            const url = context.globalVariables.get('url') || input;
            return input.replace('${url}', url);
          }
        },
        {
          toolName: 'mcpMemory',
          input: '创建实体：名称为"截图记录"，描述为"截图了网页${url}"',
          dependencies: ['mcpPuppeteer'],
          transform: (input, context) => {
            const url = context.globalVariables.get('url') || 'unknown';
            return input.replace('${url}', url);
          }
        }
      ]
    });
  }

  /**
   * 注册工具链
   */
  registerToolChain(toolChain: ToolChain): void {
    this.toolChains.set(toolChain.id, toolChain);
  }

  /**
   * 检测输入是否匹配工具链
   */
  detectToolChain(input: string): ToolChain | null {
    for (const [_, toolChain] of this.toolChains) {
      for (const trigger of toolChain.triggers) {
        if (input.includes(trigger)) {
          return toolChain;
        }
      }
    }
    return null;
  }

  /**
   * 执行工具链
   */
  async executeToolChain(
    toolChain: ToolChain, 
    input: string, 
    context: ToolContext
  ): Promise<string> {
    const chainContext: ToolChainContext = {
      userId: context.userId,
      agentId: context.agentId,
      stepResults: new Map(),
      globalVariables: new Map(),
      metadata: {}
    };

    // 解析输入中的变量
    this.parseInputVariables(input, chainContext);

    let result = `执行工具链: ${toolChain.name}\n`;
    result += `描述: ${toolChain.description}\n\n`;

    // 按依赖关系排序步骤
    const sortedSteps = this.sortStepsByDependencies(toolChain.steps);

    for (let i = 0; i < sortedSteps.length; i++) {
      const step = sortedSteps[i];
      
      // 检查执行条件
      if (step.condition && !step.condition(chainContext)) {
        result += `步骤 ${i + 1}: 跳过 ${step.toolName} (条件不满足)\n`;
        continue;
      }

      // 检查依赖
      if (step.dependencies) {
        const missingDeps = step.dependencies.filter(dep => !chainContext.stepResults.has(dep));
        if (missingDeps.length > 0) {
          result += `步骤 ${i + 1}: 跳过 ${step.toolName} (缺少依赖: ${missingDeps.join(', ')})\n`;
          continue;
        }
      }

      try {
        // 转换输入
        let stepInput = step.input;
        if (step.transform) {
          stepInput = step.transform(stepInput, chainContext);
        }

        result += `步骤 ${i + 1}: 执行 ${step.toolName} - ${stepInput}\n`;

        // 执行工具
        const stepResult = await this.executeTool(step.toolName, stepInput, context);
        chainContext.stepResults.set(step.toolName, stepResult);
        
        result += `结果: ${stepResult}\n\n`;
      } catch (error) {
        result += `错误: ${error instanceof Error ? error.message : '未知错误'}\n\n`;
      }
    }

    return result;
  }

  /**
   * 解析输入中的变量
   */
  private parseInputVariables(input: string, context: ToolChainContext): void {
    // 解析URL
    const urlMatch = input.match(/https?:\/\/[^\s]+/);
    if (urlMatch) {
      context.globalVariables.set('url', urlMatch[0]);
    }

    // 解析文件名
    const filenameMatch = input.match(/文件名?[：:]\s*([^\s]+)/);
    if (filenameMatch) {
      context.globalVariables.set('filename', filenameMatch[1]);
    }
  }

  /**
   * 按依赖关系排序步骤
   */
  private sortStepsByDependencies(steps: ToolChainStep[]): ToolChainStep[] {
    const sorted: ToolChainStep[] = [];
    const remaining = [...steps];

    while (remaining.length > 0) {
      const canExecute = remaining.filter(step => {
        if (!step.dependencies) return true;
        return step.dependencies.every(dep => 
          sorted.some(s => s.toolName === dep)
        );
      });

      if (canExecute.length === 0) {
        // 如果没有可执行的步骤，添加剩余的第一个
        sorted.push(remaining.shift()!);
      } else {
        // 添加第一个可执行的步骤
        const step = canExecute[0];
        sorted.push(step);
        remaining.splice(remaining.indexOf(step), 1);
      }
    }

    return sorted;
  }

  /**
   * 执行单个工具
   */
  private async executeTool(toolName: string, input: string, context: ToolContext): Promise<string> {
    // 首先尝试本地工具
    const localTool = this.tools.get(toolName);
    if (localTool) {
      return await localTool.run(input, context);
    }

    // 然后尝试MCP工具
    if (this.mcpManager) {
      try {
        const result = await this.mcpManager.callTool(toolName, { input });
        return JSON.stringify(result);
      } catch (error) {
        throw new Error(`MCP工具 ${toolName} 执行失败: ${error}`);
      }
    }

    throw new Error(`工具 ${toolName} 未找到`);
  }

  /**
   * 获取所有工具链
   */
  getAllToolChains(): ToolChain[] {
    return Array.from(this.toolChains.values());
  }

  /**
   * 获取工具链统计信息
   */
  getStatistics(): {
    totalChains: number;
    categoryCounts: Record<string, number>;
    totalSteps: number;
  } {
    const chains = Array.from(this.toolChains.values());
    const categoryCounts: Record<string, number> = {};
    let totalSteps = 0;

    chains.forEach(chain => {
      categoryCounts[chain.category] = (categoryCounts[chain.category] || 0) + 1;
      totalSteps += chain.steps.length;
    });

    return {
      totalChains: chains.length,
      categoryCounts,
      totalSteps
    };
  }
}

// 导出单例实例
export const toolChainManager = new ToolChainManager();
