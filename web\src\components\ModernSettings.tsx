import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Settings as SettingsIcon, 
  X, 
  Save, 
  Download, 
  Check, 
  AlertCircle, 
  Cloud, 
  HardDrive,
  Zap,
  Brain,
  Loader2
} from 'lucide-react';

interface ModelInfo {
  id: string;
  name: string;
  description: string;
  type: 'local' | 'cloud';
  size?: string;
  modified?: string;
  available: boolean;
}

interface ModernSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  currentModel: string;
  onModelChange: (model: string) => void;
}

export function ModernSettings({ isOpen, onClose, currentModel, onModelChange }: ModernSettingsProps) {
  const [selectedModel, setSelectedModel] = useState(currentModel);
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [installing, setInstalling] = useState<string | null>(null);
  const [apiKeys, setApiKeys] = useState({
    openai: '',
    anthropic: '',
    openweather: '',
    google: ''
  });
  const [systemPrompt, setSystemPrompt] = useState('你是一个智能助手，名叫Web智能体。你可以使用各种工具来帮助用户解决问题。当用户询问时间、天气、需要计算或搜索信息时，你应该主动使用相应的工具。请用友好、专业的语气与用户交流。');
  const [mcpServerUrl, setMcpServerUrl] = useState('http://localhost:3002');
  const [enableMcp, setEnableMcp] = useState(false);
  const [saved, setSaved] = useState(false);

  // 加载可用模型
  useEffect(() => {
    if (isOpen) {
      loadModels();
    }
  }, [isOpen]);

  const loadModels = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/models');
      const data = await response.json();
      setModels(data.models || []);
    } catch (error) {
      console.error('Failed to load models:', error);
    }
  };

  const installModel = async (modelId: string) => {
    setInstalling(modelId);
    try {
      const response = await fetch('http://localhost:3001/api/models/install', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ modelId })
      });

      if (response.ok) {
        // 重新加载模型列表
        await loadModels();
      } else {
        const error = await response.json();
        alert(`安装失败: ${error.error}`);
      }
    } catch (error) {
      console.error('Failed to install model:', error);
      alert('安装失败，请检查网络连接');
    }
    setInstalling(null);
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      const config = {
        model: selectedModel,
        memory: { type: 'inmemory' },
        plugins: ['getTime', 'webSearch', 'math', 'weather'],
        systemPrompt: systemPrompt,
        mcp: {
          enabled: enableMcp,
          serverUrl: mcpServerUrl
        },
        apiKeys: apiKeys
      };

      const response = await fetch('http://localhost:3001/api/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      });

      if (response.ok) {
        onModelChange(selectedModel);
        setSaved(true);
        setTimeout(() => setSaved(false), 2000);
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
    setLoading(false);
  };

  const getModelIcon = (model: ModelInfo) => {
    if (model.type === 'cloud') {
      return <Cloud className="w-5 h-5 text-blue-500" />;
    }
    return <HardDrive className="w-5 h-5 text-green-500" />;
  };

  const getModelBadge = (model: ModelInfo) => {
    if (!model.available) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          <Download className="w-3 h-3 mr-1" />
          可安装
        </span>
      );
    }
    
    if (model.type === 'cloud') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <Zap className="w-3 h-3 mr-1" />
          云端
        </span>
      );
    }
    
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <Check className="w-3 h-3 mr-1" />
        已安装
      </span>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            className="fixed inset-0 bg-black/50 z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          {/* 设置面板 */}
          <motion.div
            className="fixed right-0 top-0 h-full w-[480px] bg-white shadow-2xl z-50 overflow-y-auto"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                    <SettingsIcon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">设置</h2>
                    <p className="text-sm text-gray-500">配置您的AI助手</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* AI模型选择 */}
              <div className="mb-8">
                <div className="flex items-center gap-2 mb-4">
                  <Brain className="w-5 h-5 text-purple-600" />
                  <h3 className="text-lg font-semibold text-gray-900">AI 模型</h3>
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  选择最适合您需求的AI模型。本地模型提供隐私保护，云端模型提供更强大的能力。
                </p>
                
                <div className="space-y-3">
                  {models.map(model => (
                    <motion.div
                      key={model.id}
                      className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all ${
                        selectedModel === model.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                      onClick={() => model.available && setSelectedModel(model.id)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          {getModelIcon(model)}
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-semibold text-gray-900">{model.name}</h4>
                              {getModelBadge(model)}
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{model.description}</p>
                            {model.size && (
                              <p className="text-xs text-gray-500">大小: {model.size}</p>
                            )}
                            {model.modified && (
                              <p className="text-xs text-gray-500">更新: {model.modified}</p>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          {!model.available && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                installModel(model.id);
                              }}
                              disabled={installing === model.id}
                              className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                            >
                              {installing === model.id ? (
                                <Loader2 className="w-3 h-3 animate-spin" />
                              ) : (
                                <Download className="w-3 h-3" />
                              )}
                              {installing === model.id ? '安装中...' : '安装'}
                            </button>
                          )}
                          
                          {selectedModel === model.id && (
                            <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                              <Check className="w-3 h-3 text-white" />
                            </div>
                          )}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* 系统提示词 */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-900">系统提示词</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-2">
                    自定义AI行为和个性
                  </label>
                  <textarea
                    value={systemPrompt}
                    onChange={(e) => setSystemPrompt(e.target.value)}
                    placeholder="输入系统提示词来定制AI的行为..."
                    rows={4}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-colors"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    提示词将影响AI的回复风格和行为。建议包含工具使用指导。
                  </div>
                </div>
              </div>

              {/* 保存按钮 */}
              <div className="flex gap-3">
                <motion.button
                  onClick={saveSettings}
                  disabled={loading}
                  className="flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 transition-all font-medium"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {loading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : saved ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  {loading ? '保存中...' : saved ? '已保存！' : '保存设置'}
                </motion.button>
              </div>

              {saved && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-4 p-3 bg-green-100 border border-green-200 rounded-lg flex items-center gap-2"
                >
                  <Check className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-800">设置已保存！</span>
                </motion.div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
