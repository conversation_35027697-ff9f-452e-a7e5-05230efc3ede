/**
 * 智能体系统演示脚本
 * 展示核心功能和插件使用
 */
import { createAgent } from './src/common/factory';

async function runDemo() {
  console.log('🚀 智能体系统演示开始\n');
  
  // 创建智能体
  const agent = createAgent('demo-agent', '演示智能体');
  console.log('✅ 智能体创建成功');
  
  // 演示1: 时间工具
  console.log('\n📅 演示1: 获取当前时间');
  const timeResult = await agent.act({
    userId: 'demo-user',
    content: '#tool:getTime'
  });
  console.log('输入:', '#tool:getTime');
  console.log('输出:', timeResult.content);
  
  // 演示2: 数学计算
  console.log('\n🧮 演示2: 数学计算');
  const mathResult = await agent.act({
    userId: 'demo-user',
    content: '#tool:math 25 * 4 + 10'
  });
  console.log('输入:', '#tool:math 25 * 4 + 10');
  console.log('输出:', mathResult.content);
  
  // 演示3: 天气查询
  console.log('\n🌤️ 演示3: 天气查询');
  const weatherResult = await agent.act({
    userId: 'demo-user',
    content: '#tool:weather 北京'
  });
  console.log('输入:', '#tool:weather 北京');
  console.log('输出:', weatherResult.content.substring(0, 200) + '...');
  
  // 演示4: 网络搜索
  console.log('\n🔍 演示4: 网络搜索');
  const searchResult = await agent.act({
    userId: 'demo-user',
    content: '#tool:webSearch TypeScript'
  });
  console.log('输入:', '#tool:webSearch TypeScript');
  console.log('输出:', searchResult.content.substring(0, 200) + '...');
  
  // 演示5: 多工具调用
  console.log('\n🔧 演示5: 多工具调用');
  const multiResult = await agent.act({
    userId: 'demo-user',
    content: '请帮我#tool:getTime，然后计算#tool:math 100 / 5'
  });
  console.log('输入:', '请帮我#tool:getTime，然后计算#tool:math 100 / 5');
  console.log('输出:', multiResult.content);
  console.log('调用的工具:', multiResult.toolCalls);
  
  // 演示6: 记忆检索
  console.log('\n🧠 演示6: 记忆检索');
  const history = await agent.memory.getHistory(5);
  console.log('最近5条记忆:');
  history.forEach((record, index) => {
    console.log(`${index + 1}. ${record.substring(0, 100)}...`);
  });
  
  console.log('\n🎉 演示完成！');
  console.log('\n📝 使用说明:');
  console.log('1. 启动项目: npm run dev');
  console.log('2. 访问前端: http://localhost:5173');
  console.log('3. 访问API: http://localhost:3001');
  console.log('4. 运行测试: npm test');
}

// 运行演示
if (require.main === module) {
  runDemo().catch(console.error);
}

export { runDemo };
