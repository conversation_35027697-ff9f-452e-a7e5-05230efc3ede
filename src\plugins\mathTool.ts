/**
 * 数学计算插件
 */
import { ToolPlugin, ToolContext } from '../common/plugin';

export const mathTool: ToolPlugin = {
  name: 'math',
  description: '数学计算功能，支持基本的数学运算',
  async run(input: string, context: ToolContext): Promise<string> {
    try {
      // 清理输入，移除可能的恶意代码
      const cleanInput = input.replace(/[^0-9+\-*/().\s]/g, '');
      
      if (!cleanInput.trim()) {
        return '请提供一个数学表达式，例如: 2 + 3 * 4';
      }
      
      // 安全的数学计算
      const result = safeEval(cleanInput);
      
      if (result === null) {
        return `无法计算表达式: ${input}`;
      }
      
      return `计算结果: ${input} = ${result}`;
      
    } catch (error) {
      console.error('Math calculation error:', error);
      return `计算错误: ${error instanceof Error ? error.message : '未知错误'}`;
    }
  },
};

/**
 * 安全的数学表达式计算
 */
function safeEval(expression: string): number | null {
  try {
    // 只允许数字、基本运算符和括号
    if (!/^[0-9+\-*/().\s]+$/.test(expression)) {
      return null;
    }
    
    // 使用Function构造器进行安全计算
    const result = Function(`"use strict"; return (${expression})`)();
    
    // 检查结果是否为有效数字
    if (typeof result === 'number' && !isNaN(result) && isFinite(result)) {
      return result;
    }
    
    return null;
  } catch {
    return null;
  }
}
