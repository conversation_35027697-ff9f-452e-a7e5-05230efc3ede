@tailwind base;
@tailwind components;
@tailwind utilities;

/* 可选：自定义动画和渐变 */
body {
  @apply bg-gradient-to-br from-blue-900 via-purple-900 to-gray-900 min-h-screen;
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.app-container {
  @apply max-w-4xl mx-auto p-6 rounded-xl shadow-2xl bg-white/80 mt-10;
}

input, button {
  @apply rounded px-3 py-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400;
}

button {
  @apply bg-blue-600 text-white font-bold hover:bg-blue-700 transition;
}

.history {
  @apply mt-6;
}

.bubble {
  @apply px-4 py-2 rounded-2xl shadow mb-2 max-w-[70%] break-words;
}
.bubble.user {
  @apply bg-blue-500 text-white self-end ml-auto;
}
.bubble.agent {
  @apply bg-gray-200 text-gray-900 self-start mr-auto;
}
