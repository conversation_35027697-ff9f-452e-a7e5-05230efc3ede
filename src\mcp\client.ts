/**
 * MCP客户端实现
 */
import { MCPClient, MCPTool, MCPToolCall, MCPToolResult, MCPMessage } from './types';
import axios from 'axios';
import WebSocket from 'ws';

export class SimpleMCPClient implements MCPClient {
  private ws: WebSocket | null = null;
  private tools: MCPTool[] = [];
  private messageId = 0;
  private pendingRequests = new Map<string | number, (result: any) => void>();

  async connect(serverUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(serverUrl);
        
        this.ws.on('open', async () => {
          // 发送初始化消息
          await this.sendMessage({
            jsonrpc: '2.0',
            id: this.nextId(),
            method: 'initialize',
            params: {
              protocolVersion: '2024-11-05',
              capabilities: {
                tools: {}
              },
              clientInfo: {
                name: 'web-agent',
                version: '1.0.0'
              }
            }
          });
          
          // 获取工具列表
          await this.refreshTools();
          resolve();
        });

        this.ws.on('message', (data) => {
          try {
            const message: MCPMessage = JSON.parse(data.toString());
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse MCP message:', error);
          }
        });

        this.ws.on('error', (error) => {
          reject(error);
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  async disconnect(): Promise<void> {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  async listTools(): Promise<MCPTool[]> {
    return this.tools;
  }

  async callTool(toolCall: MCPToolCall): Promise<MCPToolResult> {
    if (!this.ws) {
      throw new Error('MCP client not connected');
    }

    return new Promise((resolve, reject) => {
      const id = this.nextId();
      
      this.pendingRequests.set(id, (result) => {
        if (result.error) {
          reject(new Error(result.error.message));
        } else {
          resolve(result.result);
        }
      });

      this.sendMessage({
        jsonrpc: '2.0',
        id,
        method: 'tools/call',
        params: {
          name: toolCall.name,
          arguments: toolCall.arguments
        }
      });

      // 超时处理
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error('Tool call timeout'));
        }
      }, 30000);
    });
  }

  private async refreshTools(): Promise<void> {
    if (!this.ws) return;

    return new Promise((resolve) => {
      const id = this.nextId();
      
      this.pendingRequests.set(id, (result) => {
        if (result.result && result.result.tools) {
          this.tools = result.result.tools;
        }
        resolve();
      });

      this.sendMessage({
        jsonrpc: '2.0',
        id,
        method: 'tools/list'
      });
    });
  }

  private handleMessage(message: MCPMessage): void {
    if (message.id && this.pendingRequests.has(message.id)) {
      const callback = this.pendingRequests.get(message.id);
      this.pendingRequests.delete(message.id);
      if (callback) {
        callback(message);
      }
    }
  }

  private async sendMessage(message: MCPMessage): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  private nextId(): number {
    return ++this.messageId;
  }
}

/**
 * HTTP-based MCP客户端（用于不支持WebSocket的MCP服务）
 */
export class HTTPMCPClient implements MCPClient {
  private baseUrl: string = '';
  private tools: MCPTool[] = [];

  async connect(serverUrl: string): Promise<void> {
    this.baseUrl = serverUrl;
    await this.refreshTools();
  }

  async disconnect(): Promise<void> {
    // HTTP客户端无需断开连接
  }

  async listTools(): Promise<MCPTool[]> {
    return this.tools;
  }

  async callTool(toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      const response = await axios.post(`${this.baseUrl}/tools/call`, {
        name: toolCall.name,
        arguments: toolCall.arguments
      }, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      throw new Error(`MCP tool call failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async refreshTools(): Promise<void> {
    try {
      const response = await axios.get(`${this.baseUrl}/tools/list`);
      this.tools = response.data.tools || [];
    } catch (error) {
      console.error('Failed to fetch MCP tools:', error);
      this.tools = [];
    }
  }
}
