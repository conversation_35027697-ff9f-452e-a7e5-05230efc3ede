/**
 * 基于官方MCP SDK的服务器实现
 */
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { z } from "zod";
import express from "express";
import cors from "cors";
import { randomUUID } from "node:crypto";

// 创建MCP服务器
const mcpServer = new McpServer({
  name: "web-agent-mcp-server",
  version: "1.0.0"
});

// 注册时间工具
mcpServer.registerTool(
  "getTime",
  {
    title: "获取当前时间",
    description: "获取当前系统时间",
    inputSchema: {}
  },
  async () => {
    const now = new Date();
    return {
      content: [{
        type: "text",
        text: now.toLocaleString('zh-CN', { 
          timeZone: 'Asia/Shanghai',
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      }]
    };
  }
);

// 注册数学计算工具
mcpServer.registerTool(
  "math",
  {
    title: "数学计算",
    description: "执行数学计算表达式",
    inputSchema: {
      expression: z.string().describe("要计算的数学表达式")
    }
  },
  async ({ expression }) => {
    try {
      // 安全的数学表达式计算
      const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
      const result = Function(`"use strict"; return (${sanitized})`)();
      
      return {
        content: [{
          type: "text",
          text: `${expression} = ${result}`
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: "text",
          text: `计算错误: ${error instanceof Error ? error.message : '无效表达式'}`
        }],
        isError: true
      };
    }
  }
);

// 注册天气查询工具
mcpServer.registerTool(
  "weather",
  {
    title: "天气查询",
    description: "查询指定城市的天气信息",
    inputSchema: {
      city: z.string().describe("要查询天气的城市名称")
    }
  },
  async ({ city }) => {
    try {
      // 使用wttr.in API获取天气信息
      const response = await fetch(`https://wttr.in/${encodeURIComponent(city)}?format=j1`);
      
      if (!response.ok) {
        throw new Error(`天气API请求失败: ${response.status}`);
      }
      
      const data = await response.json() as any;
      const current = data.current_condition[0];
      const today = data.weather[0];
      
      const weatherInfo = `${city}天气信息：
🌡️ 当前温度：${current.temp_C}°C（体感温度${current.FeelsLikeC}°C）
☁️ 天气状况：${current.weatherDesc[0].value}
💧 湿度：${current.humidity}%
💨 风速：${current.windspeedKmph} km/h
📊 今日最高/最低温度：${today.maxtempC}°C/${today.mintempC}°C
🌅 日出时间：${today.astronomy[0].sunrise}
🌇 日落时间：${today.astronomy[0].sunset}`;

      return {
        content: [{
          type: "text",
          text: weatherInfo
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: "text",
          text: `天气查询失败: ${error instanceof Error ? error.message : '未知错误'}`
        }],
        isError: true
      };
    }
  }
);

// 注册网络搜索工具
mcpServer.registerTool(
  "webSearch",
  {
    title: "网络搜索",
    description: "在互联网上搜索信息",
    inputSchema: {
      query: z.string().describe("搜索关键词")
    }
  },
  async ({ query }) => {
    try {
      // 使用DuckDuckGo API进行搜索
      const response = await fetch(`https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`);
      
      if (!response.ok) {
        throw new Error(`搜索API请求失败: ${response.status}`);
      }
      
      const data = await response.json() as any;
      
      if (data.AbstractText) {
        return {
          content: [{
            type: "text",
            text: `搜索结果：${data.AbstractText}\n来源：${data.AbstractURL || '未知'}`
          }]
        };
      } else {
        return {
          content: [{
            type: "text",
            text: `搜索"${query}"的结果：\n暂无详细摘要信息。建议使用更具体的搜索词或配置Google Search API密钥获得更好的搜索结果。`
          }]
        };
      }
    } catch (error) {
      return {
        content: [{
          type: "text",
          text: `搜索失败: ${error instanceof Error ? error.message : '未知错误'}`
        }],
        isError: true
      };
    }
  }
);

// 注册文件系统资源（示例）
mcpServer.registerResource(
  "system-info",
  "system://info",
  {
    title: "系统信息",
    description: "获取系统基本信息",
    mimeType: "text/plain"
  },
  async (uri) => {
    const systemInfo = `系统信息：
- 平台：${process.platform}
- Node.js版本：${process.version}
- 内存使用：${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB
- 运行时间：${Math.round(process.uptime())}秒`;

    return {
      contents: [{
        uri: uri.href,
        text: systemInfo
      }]
    };
  }
);

// 启动HTTP服务器（用于Streamable HTTP传输）
export function startMCPHTTPServer(port: number = 3002) {
  const app = express();
  app.use(cors({
    origin: '*',
    exposedHeaders: ['Mcp-Session-Id'],
    allowedHeaders: ['Content-Type', 'mcp-session-id'],
  }));
  app.use(express.json());

  // 存储传输会话
  const transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

  // 处理MCP请求
  app.post('/mcp', async (req, res) => {
    const sessionId = req.headers['mcp-session-id'] as string | undefined;
    let transport: StreamableHTTPServerTransport;

    if (sessionId && transports[sessionId]) {
      // 重用现有传输
      transport = transports[sessionId];
    } else {
      // 创建新传输
      transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => randomUUID(),
        onsessioninitialized: (sessionId) => {
          transports[sessionId] = transport;
        },
        enableDnsRebindingProtection: false // 开发环境禁用
      });

      // 清理传输
      transport.onclose = () => {
        if (transport.sessionId) {
          delete transports[transport.sessionId];
        }
      };

      // 连接到MCP服务器
      await mcpServer.connect(transport);
    }

    // 处理请求
    await transport.handleRequest(req, res, req.body);
  });

  // 处理SSE通知
  app.get('/mcp', async (req, res) => {
    const sessionId = req.headers['mcp-session-id'] as string | undefined;
    if (!sessionId || !transports[sessionId]) {
      res.status(400).send('Invalid or missing session ID');
      return;
    }
    
    const transport = transports[sessionId];
    await transport.handleRequest(req, res);
  });

  // 处理会话终止
  app.delete('/mcp', async (req, res) => {
    const sessionId = req.headers['mcp-session-id'] as string | undefined;
    if (!sessionId || !transports[sessionId]) {
      res.status(400).send('Invalid or missing session ID');
      return;
    }
    
    const transport = transports[sessionId];
    await transport.handleRequest(req, res);
  });

  // 健康检查
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      server: 'official-mcp-server',
      version: '1.0.0',
      tools: ['getTime', 'math', 'weather', 'webSearch']
    });
  });

  // 获取工具列表
  app.get('/tools', (req, res) => {
    res.json({
      tools: [
        { name: 'getTime', description: '获取当前时间' },
        { name: 'math', description: '数学计算' },
        { name: 'weather', description: '天气查询' },
        { name: 'webSearch', description: '网络搜索' }
      ]
    });
  });

  app.listen(port, () => {
    console.log(`Official MCP Server running on http://localhost:${port}`);
    console.log(`Available tools: getTime, math, weather, webSearch`);
    console.log(`Health check: http://localhost:${port}/health`);
  });

  return app;
}

// 启动stdio服务器（用于命令行客户端）
export async function startMCPStdioServer() {
  const transport = new StdioServerTransport();
  await mcpServer.connect(transport);
  console.log("Official MCP Server running on stdio");
}

// 如果直接运行此文件
if (require.main === module) {
  const mode = process.argv[2] || 'http';

  if (mode === 'stdio') {
    startMCPStdioServer().catch(console.error);
  } else {
    const port = parseInt(process.argv[3]) || 3002;
    startMCPHTTPServer(port);
  }
}
