/**
 * 基于官方MCP SDK的客户端实现
 */
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";

export interface MCPToolCall {
  name: string;
  arguments: Record<string, any>;
}

export interface MCPToolResult {
  content: Array<{
    type: 'text' | 'image' | 'resource';
    text?: string;
    data?: string;
    mimeType?: string;
  }>;
  isError?: boolean;
}

export class OfficialMCPClient {
  private client: Client;
  private transport: StreamableHTTPClientTransport | null = null;
  private connected = false;

  constructor() {
    this.client = new Client({
      name: "web-agent-mcp-client",
      version: "1.0.0"
    });
  }

  /**
   * 连接到MCP服务器
   */
  async connect(serverUrl: string): Promise<void> {
    try {
      // 确保URL包含MCP端点
      const mcpUrl = serverUrl.endsWith('/mcp') ? serverUrl : `${serverUrl}/mcp`;
      this.transport = new StreamableHTTPClientTransport(new URL(mcpUrl));
      await this.client.connect(this.transport);
      this.connected = true;
      console.log(`Connected to MCP server: ${mcpUrl}`);
    } catch (error) {
      console.error('Failed to connect to MCP server:', error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.transport) {
      await this.transport.close();
      this.transport = null;
    }
    this.connected = false;
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * 获取可用工具列表
   */
  async listTools(): Promise<any[]> {
    if (!this.connected) {
      throw new Error('Not connected to MCP server');
    }

    try {
      const response = await this.client.listTools();
      return response.tools || [];
    } catch (error) {
      console.error('Failed to list tools:', error);
      return [];
    }
  }

  /**
   * 调用工具
   */
  async callTool(toolCall: MCPToolCall): Promise<MCPToolResult> {
    if (!this.connected) {
      throw new Error('Not connected to MCP server');
    }

    try {
      const response = await this.client.callTool({
        name: toolCall.name,
        arguments: toolCall.arguments
      });

      return {
        content: (response as any).content || [],
        isError: (response as any).isError || false
      };
    } catch (error) {
      console.error(`Failed to call tool ${toolCall.name}:`, error);
      return {
        content: [{
          type: 'text',
          text: `工具调用失败: ${error instanceof Error ? error.message : '未知错误'}`
        }],
        isError: true
      };
    }
  }

  /**
   * 获取可用资源列表
   */
  async listResources(): Promise<any[]> {
    if (!this.connected) {
      throw new Error('Not connected to MCP server');
    }

    try {
      const response = await this.client.listResources();
      return response.resources || [];
    } catch (error) {
      console.error('Failed to list resources:', error);
      return [];
    }
  }

  /**
   * 读取资源
   */
  async readResource(uri: string): Promise<any> {
    if (!this.connected) {
      throw new Error('Not connected to MCP server');
    }

    try {
      const response = await this.client.readResource({ uri });
      return response;
    } catch (error) {
      console.error(`Failed to read resource ${uri}:`, error);
      throw error;
    }
  }
}

/**
 * MCP工具发现和自动识别
 */
export class MCPToolDiscovery {
  private client: OfficialMCPClient;
  private discoveredTools: Map<string, any> = new Map();

  constructor(client: OfficialMCPClient) {
    this.client = client;
  }

  /**
   * 自动发现所有可用工具
   */
  async discoverTools(): Promise<Map<string, any>> {
    try {
      const tools = await this.client.listTools();
      
      this.discoveredTools.clear();
      for (const tool of tools) {
        this.discoveredTools.set(tool.name, tool);
        console.log(`发现工具: ${tool.name} - ${tool.description || '无描述'}`);
      }

      return this.discoveredTools;
    } catch (error) {
      console.error('工具发现失败:', error);
      return this.discoveredTools;
    }
  }

  /**
   * 获取工具信息
   */
  getToolInfo(toolName: string): any | null {
    return this.discoveredTools.get(toolName) || null;
  }

  /**
   * 获取所有工具名称
   */
  getToolNames(): string[] {
    return Array.from(this.discoveredTools.keys());
  }

  /**
   * 检查工具是否可用
   */
  hasToolAvailable(toolName: string): boolean {
    return this.discoveredTools.has(toolName);
  }

  /**
   * 根据描述搜索工具
   */
  searchTools(keyword: string): any[] {
    const results: any[] = [];
    
    for (const [name, tool] of this.discoveredTools) {
      if (name.toLowerCase().includes(keyword.toLowerCase()) ||
          (tool.description && tool.description.toLowerCase().includes(keyword.toLowerCase()))) {
        results.push(tool);
      }
    }

    return results;
  }

  /**
   * 自动调用最合适的工具
   */
  async autoCallTool(intent: string, parameters: Record<string, any> = {}): Promise<MCPToolResult | null> {
    // 简单的意图识别
    let toolName: string | null = null;

    if (intent.includes('时间') || intent.includes('time')) {
      toolName = 'getTime';
    } else if (intent.includes('计算') || intent.includes('math') || /[\d+\-*/]/.test(intent)) {
      toolName = 'math';
      if (!parameters.expression) {
        parameters.expression = intent.replace(/[^0-9+\-*/().\s]/g, '');
      }
    } else if (intent.includes('天气') || intent.includes('weather')) {
      toolName = 'weather';
      if (!parameters.city) {
        // 尝试从意图中提取城市名
        const cityMatch = intent.match(/([北京|上海|广州|深圳|杭州|南京|武汉|成都|重庆|天津|西安|青岛|大连|厦门|苏州|无锡|宁波|长沙|郑州|济南|福州|合肥|昆明|南昌|太原|石家庄|哈尔滨|长春|沈阳|呼和浩特|银川|西宁|拉萨|乌鲁木齐|兰州|贵阳|海口|三亚|香港|澳门|台北]+)/);
        if (cityMatch) {
          parameters.city = cityMatch[1];
        }
      }
    } else if (intent.includes('搜索') || intent.includes('search')) {
      toolName = 'webSearch';
      if (!parameters.query) {
        parameters.query = intent.replace(/搜索|search/gi, '').trim();
      }
    }

    if (toolName && this.hasToolAvailable(toolName)) {
      return await this.client.callTool({ name: toolName, arguments: parameters });
    }

    return null;
  }
}

/**
 * 创建并连接MCP客户端
 */
export async function createMCPClient(serverUrl: string): Promise<OfficialMCPClient> {
  const client = new OfficialMCPClient();
  await client.connect(serverUrl);
  return client;
}
