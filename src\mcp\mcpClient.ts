/**
 * MCP客户端管理器 - 参考Cline项目的实现
 * 负责管理MCP服务器的生命周期和工具调用
 */
import { spawn, ChildProcess } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import * as fs from 'fs';
import * as path from 'path';

interface MCPServerConfig {
  command: string;
  args: string[];
  disabled: boolean;
  description: string;
  tools: string[];
  env?: Record<string, string>;
}

interface MCPServersConfig {
  mcpServers: Record<string, MCPServerConfig>;
  settings: {
    timeout: number;
    maxRetries: number;
    autoRestart: boolean;
  };
}

interface MCPServerInstance {
  name: string;
  config: MCPServerConfig;
  client: Client;
  transport: StdioClientTransport;
  process: ChildProcess | null;
  connected: boolean;
  tools: any[];
}

export class MCPClientManager {
  private servers: Map<string, MCPServerInstance> = new Map();
  private config: MCPServersConfig = { mcpServers: {}, settings: { timeout: 30000, maxRetries: 3, autoRestart: true } };
  private configPath: string;

  constructor(configPath: string = 'config/mcp_servers.json') {
    this.configPath = configPath;
    this.loadConfig();
  }

  /**
   * 加载MCP配置
   */
  private loadConfig(): void {
    try {
      const configContent = fs.readFileSync(this.configPath, 'utf-8');
      this.config = JSON.parse(configContent);
    } catch (error) {
      console.error('Failed to load MCP config:', error);
      this.config = {
        mcpServers: {},
        settings: {
          timeout: 30000,
          maxRetries: 3,
          autoRestart: true
        }
      };
    }
  }

  /**
   * 启动所有启用的MCP服务器
   */
  async startAllServers(): Promise<void> {
    const promises = Object.entries(this.config.mcpServers)
      .filter(([_, config]) => !config.disabled)
      .map(([name, config]) => this.startServer(name, config));

    await Promise.allSettled(promises);
  }

  /**
   * 启动单个MCP服务器
   */
  async startServer(name: string, config: MCPServerConfig): Promise<void> {
    try {
      console.log(`Starting MCP server: ${name}`);

      // 创建STDIO传输（会自动启动进程）
      const transport = new StdioClientTransport({
        command: config.command,
        args: config.args,
        env: config.env
      });

      // 创建MCP客户端
      const client = new Client({
        name: 'ai-agent-client',
        version: '1.0.0'
      }, {
        capabilities: {
          tools: {}
        }
      });

      // 连接到服务器
      await client.connect(transport);

      // 获取可用工具
      const toolsResult = await client.listTools();

      const serverInstance: MCPServerInstance = {
        name,
        config,
        client,
        transport,
        process: null, // StdioClientTransport管理进程
        connected: true,
        tools: toolsResult.tools || []
      };

      this.servers.set(name, serverInstance);

      console.log(`MCP server ${name} started successfully with ${serverInstance.tools.length} tools`);

      // 监听传输关闭事件
      transport.onclose = () => {
        console.log(`MCP server ${name} connection closed`);
        this.servers.delete(name);

        // 自动重启
        if (this.config.settings.autoRestart) {
          setTimeout(() => {
            this.startServer(name, config);
          }, 5000);
        }
      };

      transport.onerror = (error: Error) => {
        console.error(`MCP server ${name} error:`, error);
        this.servers.delete(name);
      };

    } catch (error) {
      console.error(`Failed to start MCP server ${name}:`, error);
    }
  }

  /**
   * 停止所有MCP服务器
   */
  async stopAllServers(): Promise<void> {
    const promises = Array.from(this.servers.keys()).map(name => this.stopServer(name));
    await Promise.allSettled(promises);
  }

  /**
   * 停止单个MCP服务器
   */
  async stopServer(name: string): Promise<void> {
    const server = this.servers.get(name);
    if (!server) return;

    try {
      await server.client.close();
      if (server.process) {
        server.process.kill();
      }
      this.servers.delete(name);
      console.log(`MCP server ${name} stopped`);
    } catch (error) {
      console.error(`Failed to stop MCP server ${name}:`, error);
    }
  }

  /**
   * 调用MCP工具
   */
  async callTool(toolName: string, args: any = {}): Promise<any> {
    // 查找包含该工具的服务器
    for (const [serverName, server] of this.servers) {
      if (!server.connected) continue;

      const tool = server.tools.find(t => t.name === toolName);
      if (tool) {
        try {
          console.log(`Calling MCP tool ${toolName} on server ${serverName}`);
          const result = await server.client.callTool({
            name: toolName,
            arguments: args
          });
          return result;
        } catch (error) {
          console.error(`Failed to call MCP tool ${toolName}:`, error);
          throw error;
        }
      }
    }

    throw new Error(`MCP tool ${toolName} not found in any connected server`);
  }

  /**
   * 获取所有可用工具
   */
  getAllTools(): Array<{ name: string; description: string; server: string; schema: any }> {
    const tools: Array<{ name: string; description: string; server: string; schema: any }> = [];
    
    for (const [serverName, server] of this.servers) {
      if (!server.connected) continue;
      
      for (const tool of server.tools) {
        tools.push({
          name: tool.name,
          description: tool.description || '',
          server: serverName,
          schema: tool.inputSchema
        });
      }
    }
    
    return tools;
  }

  /**
   * 获取服务器状态
   */
  getServerStatus(): Record<string, { connected: boolean; toolCount: number; description: string }> {
    const status: Record<string, { connected: boolean; toolCount: number; description: string }> = {};
    
    for (const [name, config] of Object.entries(this.config.mcpServers)) {
      const server = this.servers.get(name);
      status[name] = {
        connected: server?.connected || false,
        toolCount: server?.tools.length || 0,
        description: config.description
      };
    }
    
    return status;
  }

  /**
   * 重新加载配置
   */
  async reloadConfig(): Promise<void> {
    await this.stopAllServers();
    this.loadConfig();
    await this.startAllServers();
  }
}
