# 智能体任务处理能力综合测试

## 🎯 测试目标

验证智能体在以下方面的能力：
1. **工具链自动组合** - 智能识别任务并组合多个工具
2. **复杂任务分解** - 将复杂任务分解为可执行步骤
3. **错误处理和恢复** - 处理工具执行失败的情况
4. **上下文理解** - 理解任务上下文并做出合适的决策
5. **结果整合** - 将多个工具的结果整合为有意义的输出

## 📋 测试场景

### 场景1: 网页内容分析和记录
**任务描述**: "请分析 https://example.com 网站的内容，并将分析结果保存到知识图谱中"

**预期行为**:
1. 自动检测到这是一个网页分析任务
2. 触发"网页分析工具链"
3. 使用mcpFetch获取网页内容
4. 使用mcpMemory将结果保存到知识图谱
5. 提供完整的分析报告

**测试命令**: `#tool:toolChain 分析网页 https://example.com`

### 场景2: 文件处理和时间记录
**任务描述**: "创建一个名为report.txt的文件，写入当前时间信息，然后读取文件内容"

**预期行为**:
1. 检测到文件操作和时间记录需求
2. 组合文件处理工具链和时间工具
3. 获取当前时间
4. 创建文件并写入时间信息
5. 读取文件验证内容
6. 记录操作到知识图谱

**测试命令**: `#tool:toolChain 处理文件 文件名:report.txt`

### 场景3: 网页截图和分析
**任务描述**: "对 https://github.com 进行截图，并分析页面结构"

**预期行为**:
1. 识别截图和分析需求
2. 使用Puppeteer进行网页截图
3. 使用Fetch获取页面内容
4. 将结果保存到知识图谱
5. 提供综合分析报告

**测试命令**: `#tool:toolChain 网页截图 https://github.com`

### 场景4: 复杂任务分解
**任务描述**: "我需要监控一个网站的变化，定期截图并记录时间戳"

**预期行为**:
1. 使用taskDecomposer分析任务
2. 识别需要的工具组合
3. 提供详细的执行计划
4. 推荐合适的工具链

**测试命令**: `#tool:taskDecomposer 我需要监控一个网站的变化，定期截图并记录时间戳`

### 场景5: 工具链管理和优化
**任务描述**: "查看所有可用的工具链，并分析它们的使用统计"

**预期行为**:
1. 列出所有工具链
2. 显示详细统计信息
3. 提供使用建议
4. 展示工具链详情

**测试命令**: `#tool:toolChainManager 统计`

## 🔧 高级测试场景

### 场景6: 错误处理测试
**任务描述**: "分析一个不存在的网页 https://nonexistent-website-12345.com"

**预期行为**:
1. 尝试获取网页内容
2. 检测到网络错误
3. 优雅处理错误
4. 提供有意义的错误信息
5. 建议替代方案

### 场景7: 多步骤依赖测试
**任务描述**: "获取当前时间，创建以时间命名的文件，然后截图保存"

**预期行为**:
1. 正确处理步骤间的依赖关系
2. 将前一步的结果传递给下一步
3. 确保执行顺序正确
4. 处理可能的失败点

### 场景8: 上下文理解测试
**任务描述**: "帮我做一个网站的完整分析报告"

**预期行为**:
1. 询问缺失的信息（网站URL）
2. 提供分析方案建议
3. 展示可用的工具组合
4. 指导用户完成任务

## 📊 评估标准

### 功能性评估 (40分)
- [ ] 工具正确执行 (10分)
- [ ] 工具链正确触发 (10分)
- [ ] 结果正确输出 (10分)
- [ ] 依赖关系正确处理 (10分)

### 智能性评估 (30分)
- [ ] 任务自动识别 (10分)
- [ ] 工具自动组合 (10分)
- [ ] 上下文理解 (10分)

### 稳定性评估 (20分)
- [ ] 错误优雅处理 (10分)
- [ ] 异常情况恢复 (10分)

### 用户体验评估 (10分)
- [ ] 输出清晰易懂 (5分)
- [ ] 执行过程透明 (5分)

## 🚀 执行测试

### 基础功能测试
```bash
# 1. 检查MCP服务器状态
#tool:mcpStatus

# 2. 查看工具链列表
#tool:toolChainManager 列表

# 3. 查看工具链统计
#tool:toolChainManager 统计
```

### 工具链执行测试
```bash
# 1. 网页分析测试
#tool:toolChain 分析网页 https://example.com

# 2. 文件处理测试
#tool:toolChain 处理文件 文件名:test.txt

# 3. 时间记录测试
#tool:toolChain 记录时间

# 4. 网页截图测试
#tool:toolChain 网页截图 https://github.com
```

### 任务分解测试
```bash
# 1. 复杂任务分解
#tool:taskDecomposer 我需要监控网站变化并生成报告

# 2. 多工具任务分解
#tool:taskDecomposer 分析网页内容并保存到文件
```

## 📈 预期结果

### 成功指标
- ✅ 所有基础工具正常工作
- ✅ 工具链能够正确触发和执行
- ✅ 任务分解器能够理解复杂任务
- ✅ 错误处理机制有效
- ✅ 用户界面响应良好

### 性能指标
- 工具执行时间 < 30秒
- 工具链执行时间 < 60秒
- 错误恢复时间 < 10秒
- 界面响应时间 < 3秒

## 🎉 测试完成标准

当以下条件全部满足时，认为测试完成：
1. 所有8个测试场景都能正常执行
2. 功能性评估得分 ≥ 32分 (80%)
3. 智能性评估得分 ≥ 24分 (80%)
4. 稳定性评估得分 ≥ 16分 (80%)
5. 用户体验评估得分 ≥ 8分 (80%)
6. 总体评分 ≥ 80分 (80%)

这个测试将全面验证智能体的实际任务处理能力，确保它能够在真实场景中有效工作。
