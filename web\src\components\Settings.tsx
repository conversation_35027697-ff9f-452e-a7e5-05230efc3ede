import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Settings as SettingsIcon, X, Save } from 'lucide-react';

interface SettingsProps {
  isOpen: boolean;
  onClose: () => void;
  currentModel: string;
  onModelChange: (model: string) => void;
}

export function Settings({ isOpen, onClose, currentModel, onModelChange }: SettingsProps) {
  const [selectedModel, setSelectedModel] = useState(currentModel);
  const [apiKey, setApiKey] = useState('');
  const [saved, setSaved] = useState(false);

  const saveSettings = () => {
    onModelChange(selectedModel);
    setSaved(true);
    setTimeout(() => setSaved(false), 2000);
  };

  const availableModels = [
    { id: 'llama3.2:3b', name: 'Llama 3.2 3B', description: '轻量级本地模型' },
    { id: 'gpt-4', name: 'GPT-4', description: 'OpenAI GPT-4' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'OpenAI GPT-3.5' },
    { id: 'claude-3', name: 'Claude 3', description: 'Anthropic Claude 3' },
    { id: 'custom-agent', name: '自定义智能体', description: '本地智能体实现' }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            className="fixed inset-0 bg-black/50 z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
          
          {/* 设置面板 */}
          <motion.div
            className="fixed right-0 top-0 h-full w-96 bg-white shadow-2xl z-50 overflow-y-auto"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-2">
                  <SettingsIcon className="w-6 h-6 text-blue-600" />
                  <h2 className="text-xl font-bold text-gray-800">系统设置</h2>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-full transition"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* 模型选择 */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-700">AI 模型</h3>
                <div className="space-y-2">
                  {availableModels.map(model => (
                    <label key={model.id} className="flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <input
                        type="radio"
                        name="model"
                        value={model.id}
                        checked={selectedModel === model.id}
                        onChange={(e) => setSelectedModel(e.target.value)}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium">{model.name}</div>
                        <div className="text-sm text-gray-500">{model.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* API 密钥 */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-700">API 密钥</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">
                      OpenWeatherMap API Key
                    </label>
                    <input
                      type="password"
                      placeholder="输入天气API密钥（可选）"
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      配置后可获得更准确的天气信息
                    </div>
                  </div>
                </div>
              </div>

              {/* 保存按钮 */}
              <div className="flex gap-3">
                <button
                  onClick={saveSettings}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                >
                  <Save className="w-4 h-4" />
                  保存设置
                </button>
              </div>

              {saved && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-3 p-3 bg-green-100 text-green-800 rounded-lg text-center"
                >
                  设置已保存！
                </motion.div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
