/**
 * 基础智能体实现，具备独立记忆与推理能力
 */
import { Agent, AgentInput, AgentOutput, AgentMemory } from '../common/agent';
import { ToolPlugin } from '../common/plugin';

export class BasicAgent implements Agent {
  id: string;
  name: string;
  memory: AgentMemory;
  tools: ToolPlugin[];

  constructor(id: string, name: string, memory: AgentMemory, tools: ToolPlugin[] = []) {
    this.id = id;
    this.name = name;
    this.memory = memory;
    this.tools = tools;
  }

  /**
   * 智能体主推理方法
   */
  async act(input: AgentInput): Promise<AgentOutput> {
    // 记忆存储
    await this.memory.remember(`input:${Date.now()}`, input);
    // 工具调用检测（如 #tool:xxx）
    let toolCalls: string[] = [];
    let content = input.content;

    // 使用正则表达式匹配所有工具调用
    const toolPattern = /#tool:(\w+)([^#]*?)(?=#tool:|$)/g;
    const matches = [...content.matchAll(toolPattern)];

    for (const match of matches) {
      const toolName = match[1];
      const toolInput = match[2].trim();

      // 查找对应的工具
      const tool = this.tools.find(t => t.name === toolName);
      if (tool) {
        try {
          const result = await tool.run(toolInput, { userId: input.userId, agentId: this.id, memory: this.memory });
          content += `\n[工具${tool.name}结果]: ${result}`;
          toolCalls.push(tool.name);
        } catch (error) {
          content += `\n[工具${tool.name}错误]: ${error instanceof Error ? error.message : '未知错误'}`;
        }
      } else {
        content += `\n[错误]: 未找到工具 ${toolName}`;
      }
    }

    // 记忆输出
    await this.memory.remember(`output:${Date.now()}`, content);
    return { content, toolCalls };
  }
}
