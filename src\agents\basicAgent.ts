/**
 * 基础智能体实现，具备独立记忆与推理能力
 * 支持工具链自动组合和智能任务处理
 */
import { Agent, AgentInput, AgentOutput, AgentMemory } from '../common/agent';
import { ToolPlugin } from '../common/plugin';
import { toolChainManager } from '../toolchain/toolchainManager';

export class BasicAgent implements Agent {
  id: string;
  name: string;
  memory: AgentMemory;
  tools: ToolPlugin[];

  constructor(id: string, name: string, memory: AgentMemory, tools: ToolPlugin[] = []) {
    this.id = id;
    this.name = name;
    this.memory = memory;
    this.tools = tools;
  }

  /**
   * 智能体主推理方法 - 增强版本支持工具链和智能任务处理
   */
  async act(input: AgentInput): Promise<AgentOutput> {
    // 记忆存储
    await this.memory.remember(`input:${Date.now()}`, input);

    let toolCalls: string[] = [];
    let content = input.content;

    // 1. 首先检测是否为工具链任务
    const detectedChain = toolChainManager.detectToolChain(input.content);
    if (detectedChain && !input.content.includes('#tool:')) {
      try {
        const chainResult = await toolChainManager.executeToolChain(
          detectedChain,
          input.content,
          { userId: input.userId, agentId: this.id, memory: this.memory }
        );
        content = `🔗 检测到工具链任务，自动执行: ${detectedChain.name}\n\n${chainResult}`;
        toolCalls.push(`toolChain:${detectedChain.id}`);

        // 记忆输出并返回
        await this.memory.remember(`output:${Date.now()}`, content);
        return { content, toolCalls };
      } catch (error) {
        content += `\n❌ 工具链执行失败: ${error instanceof Error ? error.message : '未知错误'}`;
      }
    }

    // 2. 处理显式工具调用（如 #tool:xxx）

    // 使用正则表达式匹配所有工具调用
    const toolPattern = /#tool:(\w+)([^#]*?)(?=#tool:|$)/g;
    const matches = [...content.matchAll(toolPattern)];

    if (matches.length > 0) {
      content = input.content; // 重置内容，只显示工具结果

      for (const match of matches) {
        const toolName = match[1];
        const toolInput = match[2].trim();

        // 查找对应的工具
        const tool = this.tools.find(t => t.name === toolName);
        if (tool) {
          try {
            const startTime = Date.now();
            const result = await tool.run(toolInput, {
              userId: input.userId,
              agentId: this.id,
              memory: this.memory
            });
            const duration = Date.now() - startTime;

            content += `\n✅ [工具${tool.name}结果]: ${result}`;
            content += `\n⏱️ 执行时间: ${duration}ms`;
            toolCalls.push(tool.name);

            // 记录工具使用到记忆中
            await this.memory.remember(`tool_usage:${Date.now()}`, {
              toolName: tool.name,
              input: toolInput,
              result: result,
              duration: duration,
              success: true
            });

          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : '未知错误';
            content += `\n❌ [工具${tool.name}错误]: ${errorMsg}`;

            // 记录错误到记忆中
            await this.memory.remember(`tool_error:${Date.now()}`, {
              toolName: tool.name,
              input: toolInput,
              error: errorMsg,
              success: false
            });
          }
        } else {
          content += `\n⚠️ [错误]: 未找到工具 ${toolName}`;

          // 提供工具建议
          const availableTools = this.tools.map(t => t.name);
          const suggestions = this.findSimilarTools(toolName, availableTools);
          if (suggestions.length > 0) {
            content += `\n💡 您是否想使用: ${suggestions.join(', ')}`;
          }
        }
      }
    } else {
      // 3. 如果没有显式工具调用，进行智能任务分析
      const taskAnalysis = await this.analyzeTask(input.content);
      if (taskAnalysis.suggestions.length > 0) {
        content += `\n\n🤖 智能分析建议:\n${taskAnalysis.analysis}`;
        content += `\n\n💡 推荐操作:\n${taskAnalysis.suggestions.join('\n')}`;
      }
    }

    // 记忆输出
    await this.memory.remember(`output:${Date.now()}`, content);
    return { content, toolCalls };
  }

  /**
   * 查找相似的工具名称
   */
  private findSimilarTools(targetTool: string, availableTools: string[]): string[] {
    const suggestions: string[] = [];
    const target = targetTool.toLowerCase();

    for (const tool of availableTools) {
      const toolLower = tool.toLowerCase();

      // 检查是否包含相同的子字符串
      if (toolLower.includes(target) || target.includes(toolLower)) {
        suggestions.push(tool);
      }

      // 检查编辑距离（简单版本）
      if (this.calculateEditDistance(target, toolLower) <= 2) {
        suggestions.push(tool);
      }
    }

    return [...new Set(suggestions)].slice(0, 3); // 去重并限制数量
  }

  /**
   * 计算编辑距离（简化版本）
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const len1 = str1.length;
    const len2 = str2.length;

    if (len1 === 0) return len2;
    if (len2 === 0) return len1;

    const matrix: number[][] = [];

    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        if (str1[i - 1] === str2[j - 1]) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j] + 1,     // 删除
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j - 1] + 1  // 替换
          );
        }
      }
    }

    return matrix[len1][len2];
  }

  /**
   * 智能任务分析
   */
  private async analyzeTask(content: string): Promise<{
    analysis: string;
    suggestions: string[];
  }> {
    const analysis = {
      analysis: '',
      suggestions: [] as string[]
    };

    const contentLower = content.toLowerCase();

    // 检测任务类型并提供建议
    if (contentLower.includes('时间') || contentLower.includes('现在') || contentLower.includes('当前')) {
      analysis.analysis = '检测到时间相关查询';
      analysis.suggestions.push('💡 使用 #tool:mcpTime 获取当前时间');
      analysis.suggestions.push('💡 使用 #tool:toolChain 记录时间 来记录时间到知识图谱');
    }

    if (contentLower.includes('网页') || contentLower.includes('网站') || contentLower.includes('http')) {
      analysis.analysis = '检测到网页相关任务';
      analysis.suggestions.push('💡 使用 #tool:mcpFetch 获取网页内容');
      analysis.suggestions.push('💡 使用 #tool:toolChain 分析网页 来完整分析网页');
      analysis.suggestions.push('💡 使用 #tool:toolChain 网页截图 来截图网页');
    }

    if (contentLower.includes('文件') || contentLower.includes('保存') || contentLower.includes('读取')) {
      analysis.analysis = '检测到文件操作任务';
      analysis.suggestions.push('💡 使用 #tool:mcpFileSystem 进行文件操作');
      analysis.suggestions.push('💡 使用 #tool:toolChain 处理文件 来完整处理文件');
    }

    if (contentLower.includes('记忆') || contentLower.includes('记录') || contentLower.includes('保存信息')) {
      analysis.analysis = '检测到记忆存储任务';
      analysis.suggestions.push('💡 使用 #tool:mcpMemory 操作知识图谱');
    }

    if (contentLower.includes('工具') || contentLower.includes('帮助') || contentLower.includes('功能')) {
      analysis.analysis = '检测到工具查询任务';
      analysis.suggestions.push('💡 使用 #tool:mcpStatus 查看所有可用工具');
      analysis.suggestions.push('💡 使用 #tool:toolChainManager 列表 查看工具链');
      analysis.suggestions.push('💡 使用 #tool:taskDecomposer 分解复杂任务');
    }

    return analysis;
  }
}
