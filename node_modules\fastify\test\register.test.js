'use strict'

const { test } = require('node:test')
const sget = require('simple-get').concat
const Fastify = require('..')

test('register', async (t) => {
  t.plan(14)

  const fastify = Fastify()

  fastify.register(function (instance, opts, done) {
    t.assert.notStrictEqual(instance, fastify)
    t.assert.ok(Object.prototype.isPrototypeOf.call(fastify, instance))

    t.assert.strictEqual(typeof opts, 'object')
    t.assert.strictEqual(typeof done, 'function')

    instance.get('/first', function (req, reply) {
      reply.send({ hello: 'world' })
    })
    done()
  })

  fastify.register(function (instance, opts, done) {
    t.assert.notStrictEqual(instance, fastify)
    t.assert.ok(Object.prototype.isPrototypeOf.call(fastify, instance))

    t.assert.strictEqual(typeof opts, 'object')
    t.assert.strictEqual(typeof done, 'function')

    instance.get('/second', function (req, reply) {
      reply.send({ hello: 'world' })
    })
    done()
  })

  await fastify.listen({ port: 0 })
  t.after(() => fastify.close())

  await makeRequest('first')
  await makeRequest('second')

  async function makeRequest (path) {
    return new Promise((resolve, reject) => {
      sget({
        method: 'GET',
        url: 'http://localhost:' + fastify.server.address().port + '/' + path
      }, (err, response, body) => {
        if (err) {
          t.assert.ifError(err)
          return reject(err)
        }
        t.assert.strictEqual(response.statusCode, 200)
        t.assert.strictEqual(response.headers['content-length'], '' + body.length)
        t.assert.deepStrictEqual(JSON.parse(body), { hello: 'world' })
        resolve()
      })
    })
  }
})

test('internal route declaration should pass the error generated by the register to the done handler / 1', (t, done) => {
  t.plan(1)
  const fastify = Fastify()

  fastify.register((instance, opts, done) => {
    done(new Error('kaboom'))
  })

  fastify.get('/', (req, reply) => {
    reply.send({ hello: 'world' })
  })

  fastify.listen({ port: 0 }, err => {
    t.after(() => fastify.close())
    t.assert.strictEqual(err.message, 'kaboom')
    done()
  })
})

test('internal route declaration should pass the error generated by the register to the done handler / 2', (t, done) => {
  t.plan(2)
  const fastify = Fastify()

  fastify.register((instance, opts, done) => {
    done(new Error('kaboom'))
  })

  fastify.get('/', (req, reply) => {
    reply.send({ hello: 'world' })
  })

  fastify.after(err => {
    t.assert.strictEqual(err.message, 'kaboom')
  })

  fastify.listen({ port: 0 }, err => {
    t.after(() => fastify.close())
    t.assert.ifError(err)
    done()
  })
})

test('awaitable register and after', async t => {
  const fastify = Fastify()
  let first = false
  let second = false
  let third = false

  await fastify.register(async (instance, opts) => {
    first = true
  })

  t.assert.strictEqual(first, true)

  fastify.register(async (instance, opts) => {
    second = true
  })

  await fastify.after()
  t.assert.strictEqual(second, true)

  fastify.register(async (instance, opts) => {
    third = true
  })

  await fastify.ready()
  t.assert.strictEqual(third, true)
})

function thenableRejects (t, promise, error) {
  return t.assert.rejects(async () => { await promise }, error)
}

test('awaitable register error handling', async t => {
  const fastify = Fastify()

  const e = new Error('kaboom')

  await thenableRejects(t, fastify.register(async (instance, opts) => {
    throw e
  }), e)

  fastify.register(async (instance, opts) => {
    t.assert.fail('should not be executed')
  })

  await t.assert.rejects(fastify.after(), e)

  fastify.register(async (instance, opts) => {
    t.assert.fail('should not be executed')
  })

  await thenableRejects(t, fastify.ready(), e)
})

test('awaitable after error handling', async t => {
  const fastify = Fastify()

  const e = new Error('kaboom')

  fastify.register(async (instance, opts) => {
    throw e
  })

  fastify.register(async (instance, opts) => {
    t.assert.fail('should not be executed')
  })

  await t.assert.rejects(fastify.after(), e)

  fastify.register(async (instance, opts) => {
    t.assert.fail('should not be executed')
  })

  await t.assert.rejects(fastify.ready())
})

test('chainable register', async t => {
  t.plan(3)

  const fastify = Fastify()

  fastify.register(async () => {
    t.assert.ok('first loaded')
  }).register(async () => {
    t.assert.ok('second loaded')
  }).register(async () => {
    t.assert.ok('third loaded')
  })

  await fastify.ready()
})
