/**
 * Playwright自动化浏览器工具
 */
import { ToolPlugin, ToolContext } from '../common/plugin';
import { chromium, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext } from 'playwright';

interface BrowserSession {
  browser: Browser;
  context: BrowserContext;
  page: Page;
  sessionId: string;
  createdAt: Date;
}

class PlaywrightManager {
  private sessions: Map<string, BrowserSession> = new Map();
  private defaultSessionId = 'default';

  /**
   * 获取或创建浏览器会话
   */
  async getSession(sessionId: string = this.defaultSessionId): Promise<BrowserSession> {
    if (this.sessions.has(sessionId)) {
      return this.sessions.get(sessionId)!;
    }

    // 创建新的浏览器会话
    const browser = await chromium.launch({
      headless: false, // 显示浏览器窗口，便于调试
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const context = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    });

    const page = await context.newPage();

    const session: BrowserSession = {
      browser,
      context,
      page,
      sessionId,
      createdAt: new Date()
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * 关闭浏览器会话
   */
  async closeSession(sessionId: string = this.defaultSessionId): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      await session.browser.close();
      this.sessions.delete(sessionId);
    }
  }

  /**
   * 关闭所有会话
   */
  async closeAllSessions(): Promise<void> {
    for (const [sessionId] of this.sessions) {
      await this.closeSession(sessionId);
    }
  }

  /**
   * 清理过期会话（超过30分钟）
   */
  async cleanupExpiredSessions(): Promise<void> {
    const now = new Date();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.sessions) {
      const ageMinutes = (now.getTime() - session.createdAt.getTime()) / (1000 * 60);
      if (ageMinutes > 30) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      await this.closeSession(sessionId);
    }
  }
}

const playwrightManager = new PlaywrightManager();

// 定期清理过期会话
setInterval(() => {
  playwrightManager.cleanupExpiredSessions().catch(console.error);
}, 5 * 60 * 1000); // 每5分钟清理一次

export const playwrightTool: ToolPlugin = {
  name: 'browser',
  description: '自动化浏览器操作，支持网页导航、元素交互、数据抓取等',
  
  async run(input: string, context: ToolContext): Promise<string> {
    try {
      const command = input.trim();
      
      // 解析命令
      const parts = command.split(' ');
      const action = parts[0].toLowerCase();
      const args = parts.slice(1);

      switch (action) {
        case 'goto':
        case 'navigate':
          return await navigateToUrl(args.join(' '));
        
        case 'click':
          return await clickElement(args.join(' '));
        
        case 'type':
        case 'fill':
          return await typeText(args);
        
        case 'screenshot':
          return await takeScreenshot(args[0]);
        
        case 'extract':
        case 'get':
          return await extractContent(args.join(' '));
        
        case 'wait':
          return await waitForElement(args.join(' '));
        
        case 'scroll':
          return await scrollPage(args[0]);
        
        case 'close':
          return await closeBrowser(args[0]);
        
        case 'title':
          return await getPageTitle();
        
        case 'url':
          return await getCurrentUrl();
        
        case 'back':
          return await goBack();
        
        case 'forward':
          return await goForward();
        
        case 'refresh':
          return await refreshPage();
        
        default:
          return `不支持的浏览器操作: ${action}
          
支持的操作：
- goto <url> - 导航到指定URL
- click <selector> - 点击元素
- type <selector> <text> - 在元素中输入文本
- screenshot [filename] - 截图
- extract <selector> - 提取元素内容
- wait <selector> - 等待元素出现
- scroll <direction> - 滚动页面 (up/down)
- title - 获取页面标题
- url - 获取当前URL
- back - 后退
- forward - 前进
- refresh - 刷新页面
- close [sessionId] - 关闭浏览器

示例：
- browser goto https://www.baidu.com
- browser click input[name="wd"]
- browser type input[name="wd"] 人工智能
- browser screenshot baidu.png`;
      }
    } catch (error) {
      return `浏览器操作失败: ${error instanceof Error ? error.message : '未知错误'}`;
    }
  }
};

/**
 * 导航到URL
 */
async function navigateToUrl(url: string): Promise<string> {
  const session = await playwrightManager.getSession();
  
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    url = 'https://' + url;
  }
  
  await session.page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
  const title = await session.page.title();
  
  return `已导航到: ${url}\n页面标题: ${title}`;
}

/**
 * 点击元素
 */
async function clickElement(selector: string): Promise<string> {
  const session = await playwrightManager.getSession();
  
  await session.page.waitForSelector(selector, { timeout: 10000 });
  await session.page.click(selector);
  
  return `已点击元素: ${selector}`;
}

/**
 * 输入文本
 */
async function typeText(args: string[]): Promise<string> {
  if (args.length < 2) {
    return '请提供选择器和要输入的文本';
  }
  
  const session = await playwrightManager.getSession();
  const selector = args[0];
  const text = args.slice(1).join(' ');
  
  await session.page.waitForSelector(selector, { timeout: 10000 });
  await session.page.fill(selector, text);
  
  return `已在 ${selector} 中输入: ${text}`;
}

/**
 * 截图
 */
async function takeScreenshot(filename?: string): Promise<string> {
  const session = await playwrightManager.getSession();
  
  const screenshotPath = filename || `screenshot-${Date.now()}.png`;
  await session.page.screenshot({ path: screenshotPath, fullPage: true });
  
  return `已保存截图: ${screenshotPath}`;
}

/**
 * 提取内容
 */
async function extractContent(selector: string): Promise<string> {
  const session = await playwrightManager.getSession();
  
  try {
    await session.page.waitForSelector(selector, { timeout: 5000 });
    const content = await session.page.textContent(selector);
    return `提取的内容: ${content || '(空内容)'}`;
  } catch {
    return `未找到元素: ${selector}`;
  }
}

/**
 * 等待元素
 */
async function waitForElement(selector: string): Promise<string> {
  const session = await playwrightManager.getSession();
  
  await session.page.waitForSelector(selector, { timeout: 30000 });
  return `元素已出现: ${selector}`;
}

/**
 * 滚动页面
 */
async function scrollPage(direction: string): Promise<string> {
  const session = await playwrightManager.getSession();
  
  if (direction === 'down') {
    await session.page.keyboard.press('PageDown');
  } else if (direction === 'up') {
    await session.page.keyboard.press('PageUp');
  } else {
    return '请指定滚动方向: up 或 down';
  }
  
  return `已滚动页面: ${direction}`;
}

/**
 * 关闭浏览器
 */
async function closeBrowser(sessionId?: string): Promise<string> {
  await playwrightManager.closeSession(sessionId);
  return `已关闭浏览器会话: ${sessionId || 'default'}`;
}

/**
 * 获取页面标题
 */
async function getPageTitle(): Promise<string> {
  const session = await playwrightManager.getSession();
  const title = await session.page.title();
  return `页面标题: ${title}`;
}

/**
 * 获取当前URL
 */
async function getCurrentUrl(): Promise<string> {
  const session = await playwrightManager.getSession();
  const url = session.page.url();
  return `当前URL: ${url}`;
}

/**
 * 后退
 */
async function goBack(): Promise<string> {
  const session = await playwrightManager.getSession();
  await session.page.goBack();
  return '已后退到上一页';
}

/**
 * 前进
 */
async function goForward(): Promise<string> {
  const session = await playwrightManager.getSession();
  await session.page.goForward();
  return '已前进到下一页';
}

/**
 * 刷新页面
 */
async function refreshPage(): Promise<string> {
  const session = await playwrightManager.getSession();
  await session.page.reload();
  return '已刷新页面';
}
