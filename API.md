# API 文档

## 基础信息
- 基础URL: `http://localhost:3001`
- 内容类型: `application/json`
- 认证: 无需认证（开发环境）

## 端点列表

### 1. 智能体对话
**POST** `/api/agent/act`

与智能体进行对话，支持工具调用。

#### 请求体
```json
{
  "userId": "string",
  "content": "string"
}
```

#### 响应
```json
{
  "role": "agent",
  "content": "string"
}
```

#### 示例
```bash
curl -X POST http://localhost:3001/api/agent/act \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "user-1",
    "content": "你好，请帮我#tool:getTime"
  }'
```

### 2. 获取聊天历史
**GET** `/api/agent/history`

获取最近的聊天记录。

#### 响应
```json
[
  {
    "role": "user|agent",
    "content": "string",
    "timestamp": "ISO 8601 date string"
  }
]
```

#### 示例
```bash
curl http://localhost:3001/api/agent/history
```

### 3. 清空聊天记录
**DELETE** `/api/agent/history`

清空所有聊天记录。

#### 响应
```json
{
  "success": true,
  "message": "聊天记录已清空"
}
```

#### 示例
```bash
curl -X DELETE http://localhost:3001/api/agent/history
```

### 4. 获取可用插件
**GET** `/api/agent/plugins`

获取所有可用的插件列表。

#### 响应
```json
[
  {
    "name": "string",
    "description": "string"
  }
]
```

#### 示例
```bash
curl http://localhost:3001/api/agent/plugins
```

## 工具调用格式

在对话内容中使用以下格式调用工具：

### 单工具调用
```
#tool:toolName parameters
```

### 多工具调用
```
#tool:tool1 params1 #tool:tool2 params2
```

### 可用工具

#### 1. getTime - 获取当前时间
```
#tool:getTime
```

#### 2. math - 数学计算
```
#tool:math 10 + 5 * 2
#tool:math (100 - 20) / 4
```

#### 3. weather - 天气查询
```
#tool:weather 北京
#tool:weather Shanghai
```

#### 4. webSearch - 网络搜索
```
#tool:webSearch 人工智能
#tool:webSearch machine learning
```

## 错误处理

### 错误响应格式
```json
{
  "error": "string",
  "message": "string",
  "code": "number"
}
```

### 常见错误码
- `400` - 请求参数错误
- `404` - 资源不存在
- `500` - 服务器内部错误

## 限制和注意事项

1. **请求频率**: 无限制（开发环境）
2. **消息长度**: 建议不超过 10000 字符
3. **工具超时**: 各工具有不同的超时设置
4. **并发连接**: 支持多个并发连接

## 开发指南

### 添加新的API端点
1. 在 `src/api/server.ts` 中添加路由
2. 实现相应的处理逻辑
3. 更新此文档

### 自定义错误处理
```typescript
fastify.setErrorHandler((error, request, reply) => {
  reply.status(500).send({
    error: 'Internal Server Error',
    message: error.message
  });
});
```
