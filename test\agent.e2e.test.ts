import { createAgent } from '../src/common/factory';

describe('智能体系统集成测试', () => {
  it('应能调用时间工具并写入记忆', async () => {
    const agent = createAgent('agent-test', '测试智能体');
    const input = {
      userId: 'user-1',
      content: '请帮我#tool:getTime',
    };
    const output = await agent.act(input);
    expect(output.content).toMatch(/工具getTime结果/);
    expect(output.toolCalls).toContain('getTime');
    const history = await agent.memory.getHistory(10);
    expect(history.length).toBeGreaterThanOrEqual(2);
  });

  it('应能调用数学计算工具', async () => {
    const agent = createAgent('agent-test-math', '数学测试智能体');
    const input = {
      userId: 'user-1',
      content: '#tool:math 5 + 3',
    };
    const output = await agent.act(input);
    expect(output.content).toMatch(/工具math结果/);
    expect(output.content).toMatch(/= 8/);
    expect(output.toolCalls).toContain('math');
  });

  it('应能调用天气查询工具', async () => {
    const agent = createAgent('agent-test-weather', '天气测试智能体');
    const input = {
      userId: 'user-1',
      content: '#tool:weather 北京',
    };
    const output = await agent.act(input);
    expect(output.content).toMatch(/工具weather结果/);
    expect(output.content).toMatch(/当前天气|天气查询失败/); // 允许失败情况
    expect(output.toolCalls).toContain('weather');
  }, 15000); // 增加超时时间到15秒

  it('应能调用网络搜索工具', async () => {
    const agent = createAgent('agent-test-search', '搜索测试智能体');
    const input = {
      userId: 'user-1',
      content: '#tool:webSearch 人工智能',
    };
    const output = await agent.act(input);
    expect(output.content).toMatch(/工具webSearch结果/);
    expect(output.content).toMatch(/搜索关键词/);
    expect(output.toolCalls).toContain('webSearch');
  });

  it('应能处理多个工具调用', async () => {
    const agent = createAgent('agent-test-multi', '多工具测试智能体');
    const input = {
      userId: 'user-1',
      content: '请帮我#tool:getTime，然后计算#tool:math 10 * 2',
    };
    const output = await agent.act(input);
    expect(output.content).toMatch(/工具getTime结果/);
    expect(output.content).toMatch(/工具math结果/);
    expect(output.toolCalls).toContain('getTime');
    expect(output.toolCalls).toContain('math');
  });

  it('应能检索历史并回顾内容', async () => {
    const agent = createAgent('agent-test2', '测试智能体2');
    await agent.act({ userId: 'u', content: 'first' });
    await agent.act({ userId: 'u', content: 'second' });
    const history = await agent.memory.getHistory(4); // 获取全部输入输出
    // 只取输入记录
    const inputs = history
      .map(h => { try { return JSON.parse(h); } catch { return null; } })
      .filter(obj => obj && obj.content && !obj.startsWith); // 过滤掉输出字符串
    expect(inputs[0].content).toBe('first');
    expect(inputs[1].content).toBe('second');
  });
});
