import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './App.css';

interface Message {
  role: 'user' | 'agent';
  content: string;
}

const AGENT_NAME = '智能体';

function App() {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [plugins] = useState([
    { name: 'getTime', desc: '获取当前时间' },
    { name: 'webSearch', desc: '网络搜索' },
    { name: 'math', desc: '数学计算' },
    { name: 'weather', desc: '天气查询' },
  ]);
  const [models] = useState(['llama3.2:3b', 'gpt-4', 'custom-agent']);
  const [currentModel, setCurrentModel] = useState(models[0]);
  const endRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    endRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 首次加载历史
  useEffect(() => {
    fetch('http://localhost:3001/api/agent/history')
      .then(res => res.json())
      .then((msgs: Message[]) => setMessages(msgs));
  }, []);

  const handleSend = async () => {
    if (!input.trim()) return;
    setMessages([...messages, { role: 'user', content: input }]);
    setInput('');
    setLoading(true);
    // 联调后端API
    const res = await fetch('http://localhost:3001/api/agent/act', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId: 'web-user', content: input }),
    });
    const data = await res.json();
    setMessages(msgs => [...msgs, { role: 'agent', content: data.content }]);
    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-gray-900 flex">
      {/* 侧边栏 */}
      <aside className="w-64 bg-white/80 shadow-2xl rounded-r-3xl p-6 flex flex-col gap-8">
        <div>
          <h2 className="font-bold text-lg mb-2">模型选择</h2>
          <select className="w-full p-2 rounded" value={currentModel} onChange={e => setCurrentModel(e.target.value)}>
            {models.map(m => <option key={m}>{m}</option>)}
          </select>
        </div>
        <div>
          <h2 className="font-bold text-lg mb-2">插件工具</h2>
          <ul className="space-y-2">
            {plugins.map(p => (
              <li key={p.name} className="bg-blue-100 rounded px-3 py-1 text-blue-800 text-sm font-mono cursor-pointer hover:bg-blue-200 transition" onClick={() => setInput(`#tool:${p.name}`)}>
                #{p.name} <span className="text-gray-500">{p.desc}</span>
              </li>
            ))}
          </ul>
        </div>
        <div>
          <h2 className="font-bold text-lg mb-2">记忆/历史</h2>
          <div className="h-32 overflow-y-auto bg-gray-50 rounded p-2 text-xs">
            {messages.filter(m => m.role === 'user').map((m, i) => <div key={i}>{m.content}</div>)}
          </div>
        </div>
      </aside>
      {/* 主区 */}
      <main className="flex-1 flex flex-col justify-end p-8">
        <div className="flex-1 overflow-y-auto flex flex-col gap-2 mb-4">
          <AnimatePresence initial={false}>
            {messages.map((msg, idx) => (
              <motion.div
                key={idx}
                className={`bubble ${msg.role} flex items-end`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {msg.role === 'agent' && <img src="/vite.svg" alt="agent" className="w-8 h-8 rounded-full mr-2" />}
                <div className={`rounded-2xl px-4 py-2 shadow ${msg.role === 'user' ? 'bg-blue-500 text-white ml-auto' : 'bg-gray-200 text-gray-900 mr-auto'}`}>{msg.content}</div>
                {msg.role === 'user' && <img src="/assets/react.svg" alt="user" className="w-8 h-8 rounded-full ml-2" />}
              </motion.div>
            ))}
            {loading && (
              <motion.div
                className="bubble agent animate-pulse bg-gray-300 text-gray-500"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {AGENT_NAME} 正在思考...
              </motion.div>
            )}
          </AnimatePresence>
          <div ref={endRef} />
        </div>
        <div className="flex gap-2 items-center">
          <input
            className="flex-1 p-3 rounded-2xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
            placeholder="请输入任务或提问，如 #tool:getTime"
            value={input}
            onChange={e => setInput(e.target.value)}
            onKeyDown={e => e.key === 'Enter' && handleSend()}
            disabled={loading}
          />
          <button className="px-6 py-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold shadow-lg hover:scale-105 transition" onClick={handleSend} disabled={loading}>发送</button>
        </div>
      </main>
    </div>
  );
}

export default App;
