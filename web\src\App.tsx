import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import { Settings as SettingsIcon, Send, Trash2, Clock, Search, Calculator, Cloud } from 'lucide-react';
import { ModernSettings } from './components/ModernSettings';
import './App.css';

interface Message {
  role: 'user' | 'agent';
  content: string;
  timestamp: Date;
}

const AGENT_NAME = '智能体';

function App() {
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [plugins] = useState([
    { name: 'getTime', desc: '获取当前时间', icon: Clock },
    { name: 'webSearch', desc: '网络搜索', icon: Search },
    { name: 'math', desc: '数学计算', icon: Calculator },
    { name: 'weather', desc: '天气查询', icon: Cloud },
  ]);
  const [models] = useState(['llama3.2:3b', 'gpt-4', 'custom-agent']);
  const [currentModel, setCurrentModel] = useState(models[0]);
  const [showSettings, setShowSettings] = useState(false);
  const endRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    endRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // 首次加载历史
  useEffect(() => {
    fetch('http://localhost:3001/api/agent/history')
      .then(res => res.json())
      .then((msgs: any[]) => {
        const formattedMsgs = msgs.map(msg => ({
          ...msg,
          timestamp: new Date()
        }));
        setMessages(formattedMsgs);
      });
  }, []);

  const handleSend = async () => {
    if (!input.trim()) return;
    const userMessage: Message = {
      role: 'user',
      content: input,
      timestamp: new Date()
    };
    setMessages([...messages, userMessage]);
    setInput('');
    setLoading(true);

    try {
      // 联调后端API
      const res = await fetch('http://localhost:3001/api/agent/act', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: 'web-user', content: input }),
      });
      const data = await res.json();
      const agentMessage: Message = {
        role: 'agent',
        content: data.content,
        timestamp: new Date()
      };
      setMessages(msgs => [...msgs, agentMessage]);
    } catch (error) {
      const errorMessage: Message = {
        role: 'agent',
        content: '抱歉，发生了错误，请稍后重试。',
        timestamp: new Date()
      };
      setMessages(msgs => [...msgs, errorMessage]);
    }
    setLoading(false);
  };

  const handleClearChat = async () => {
    try {
      await fetch('http://localhost:3001/api/agent/history', {
        method: 'DELETE',
      });
      setMessages([]);
    } catch (error) {
      console.error('Failed to clear chat:', error);
      // 即使API调用失败，也清空本地消息
      setMessages([]);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-gray-900 flex">
      {/* 侧边栏 */}
      <aside className="w-64 bg-white/80 shadow-2xl rounded-r-3xl p-6 flex flex-col gap-6">
        {/* 头部 */}
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-800">{AGENT_NAME}</h1>
          <button
            onClick={() => setShowSettings(true)}
            className="p-2 hover:bg-gray-100 rounded-full transition"
            title="设置"
          >
            <SettingsIcon className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        <div>
          <h2 className="font-bold text-lg mb-2 text-gray-700">当前模型</h2>
          <div className="p-3 bg-gray-100 rounded-lg">
            <div className="font-medium text-sm">{currentModel}</div>
            <div className="text-xs text-gray-500">点击设置按钮切换模型</div>
          </div>
        </div>

        <div>
          <h2 className="font-bold text-lg mb-2 text-gray-700">插件工具</h2>
          <ul className="space-y-2">
            {plugins.map(p => {
              const IconComponent = p.icon;
              return (
                <li
                  key={p.name}
                  className="bg-blue-100 rounded-lg px-3 py-2 text-blue-800 text-sm cursor-pointer hover:bg-blue-200 transition flex items-center gap-2"
                  onClick={() => setInput(`#tool:${p.name} `)}
                >
                  <IconComponent className="w-4 h-4" />
                  <span className="font-mono">#{p.name}</span>
                  <span className="text-gray-500 text-xs">{p.desc}</span>
                </li>
              );
            })}
          </ul>
        </div>
        <div>
          <h2 className="font-bold text-lg mb-2">记忆/历史</h2>
          <div className="h-32 overflow-y-auto bg-gray-50 rounded p-2 text-xs">
            {messages.filter(m => m.role === 'user').map((m, i) => <div key={i}>{m.content}</div>)}
          </div>
          <button
            onClick={handleClearChat}
            className="mt-2 w-full px-3 py-2 bg-red-500 text-white rounded-lg text-sm hover:bg-red-600 transition flex items-center justify-center gap-2"
          >
            <Trash2 className="w-4 h-4" />
            清空聊天
          </button>
        </div>
      </aside>
      {/* 主区 */}
      <main className="flex-1 flex flex-col justify-end p-8">
        <div className="flex-1 overflow-y-auto flex flex-col gap-2 mb-4">
          <AnimatePresence initial={false}>
            {messages.map((msg, idx) => (
              <motion.div
                key={idx}
                className={`bubble ${msg.role} flex items-end mb-4`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {msg.role === 'agent' && <img src="/vite.svg" alt="agent" className="w-8 h-8 rounded-full mr-2" />}
                <div className={`rounded-2xl px-4 py-3 shadow max-w-3xl ${msg.role === 'user' ? 'bg-blue-500 text-white ml-auto' : 'bg-gray-200 text-gray-900 mr-auto'}`}>
                  <div className="whitespace-pre-wrap break-words">
                    <ReactMarkdown>{msg.content}</ReactMarkdown>
                  </div>
                  <div className={`text-xs mt-2 opacity-70 ${msg.role === 'user' ? 'text-blue-100' : 'text-gray-500'}`}>
                    {msg.timestamp.toLocaleTimeString()}
                  </div>
                </div>
                {msg.role === 'user' && <img src="/assets/react.svg" alt="user" className="w-8 h-8 rounded-full ml-2" />}
              </motion.div>
            ))}
            {loading && (
              <motion.div
                className="bubble agent animate-pulse bg-gray-300 text-gray-500"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
              >
                {AGENT_NAME} 正在思考...
              </motion.div>
            )}
          </AnimatePresence>
          <div ref={endRef} />
        </div>
        <div className="flex gap-2 items-center">
          <input
            className="flex-1 p-3 rounded-2xl border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
            placeholder="请输入任务或提问，如 #tool:getTime"
            value={input}
            onChange={e => setInput(e.target.value)}
            onKeyDown={e => e.key === 'Enter' && handleSend()}
            disabled={loading}
          />
          <button
            className="px-6 py-3 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold shadow-lg hover:scale-105 transition flex items-center gap-2"
            onClick={handleSend}
            disabled={loading}
          >
            <Send className="w-4 h-4" />
            发送
          </button>
        </div>
      </main>

      {/* 设置组件 */}
      <ModernSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        currentModel={currentModel}
        onModelChange={setCurrentModel}
      />
    </div>
  );
}

export default App;
