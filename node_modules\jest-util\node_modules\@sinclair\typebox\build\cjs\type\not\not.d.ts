import type { TSchema, SchemaOptions } from '../schema/index';
import type { Static } from '../static/index';
import { Kind } from '../symbols/index';
export interface TNot<T extends TSchema = TSchema> extends TSchema {
    [Kind]: 'Not';
    static: T extends TNot<infer U> ? Static<U> : unknown;
    not: T;
}
/** `[J<PERSON>]` Creates a Not type */
export declare function Not<Type extends TSchema>(type: Type, options?: SchemaOptions): TNot<Type>;
