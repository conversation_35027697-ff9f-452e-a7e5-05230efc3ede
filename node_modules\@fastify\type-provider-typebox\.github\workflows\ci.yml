name: CI

on:
  push:
    branches:
     - main
     - next
     - 'v*'
    paths-ignore:
      - 'docs/**'
      - '*.md'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '*.md'

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: "${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}"
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  dependency-review:
      name: Dependency Review
      if: github.event_name == 'pull_request'
      runs-on: ubuntu-latest
      permissions:
        contents: read
      steps:
          - name: Check out repo
            uses: actions/checkout@v4
            with:
              persist-credentials: false

          - name: Dependency review
            uses: actions/dependency-review-action@da24556b548a50705dd671f47852072ea4c105d9 # v4.7.1

  variants:
      name: Read test variants
      runs-on: ubuntu-latest
      permissions:
        contents: read
      outputs:
        variants: ${{ steps.read-variants.outputs.variants }}
      steps:
        - name: Check out repo
          uses: actions/checkout@v4
          with:
            persist-credentials: false
        - name: Read variants
          id: read-variants
          run: |
            echo "variants=$(cat ./test-variants.json)" >> "$GITHUB_OUTPUT"

  test:
      needs: variants
      name: Test
      runs-on: ${{ matrix.os }}
      permissions:
        contents: read
      strategy:
        matrix:
          node-version: [20, 22, 24]
          peer-version: ${{ fromJSON(needs.variants.outputs.variants) }}
          os: [macos-latest, ubuntu-latest, windows-latest]
          exclude:
            - os: windows-latest
              node-version: 14
            - os: macos-latest
              node-version: 14
            - os: macos-latest
              node-version: 16
      steps:
        - name: Check out repo
          uses: actions/checkout@v4
          with:
            persist-credentials: false

        - name: Setup Node ${{ matrix.node-version }}
          uses: actions/setup-node@v4
          with:
            check-latest: true
            node-version: ${{ matrix.node-version }}

        - name: Install dependencies
          run: npm i --ignore-scripts

        - name: Install peerdeps
          run: npm i --ignore-scripts fastify @sinclair/typebox@${{ matrix.peer-version }}

        - name: Lint code
          if: ${{ matrix.node-version != 14 }}
          run: npm run lint

        - name: Run tests
          run: npm test

  automerge:
    name: Automatically merge Dependabot pull requests
    if: >
        github.event_name == 'pull_request' &&
        github.event.pull_request.user.login == 'dependabot[bot]'
    needs: test
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    steps:
      - uses: fastify/github-action-merge-dependabot@e820d631adb1d8ab16c3b93e5afe713450884a4a # v3.11.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
